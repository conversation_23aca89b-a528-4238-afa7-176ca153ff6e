"""User Repository.

This module provides data access layer for User entities, extending the base
repository with user-specific query methods and operations.
"""

from typing import List, Optional

from sqlalchemy import and_, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession


from src.config.logging_config import logger

# Unified systems imports
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.user import User
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository


class UserRepository(BaseRepository[User]):
    """Repository for User entity data access operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the User repository."""
        super().__init__(db_session, User)
        logger.debug("UserRepository initialized")

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email address (case-insensitive)."""
        logger.debug(f"Retrieving user by email: {email}")
        normalized_email = email.lower().strip()
        stmt = select(self.model).where(
            and_(
                func.lower(self.model.email) == normalized_email,
                self.model.is_active == True,
            )
        )
        result = await self.db_session.execute(stmt)
        user = result.scalar_one_or_none()
        if user:
            logger.debug(f"User found for email: {email}")
        else:
            logger.debug(f"No user found for email: {email}")
        return user

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def check_email_exists(self, email: str, exclude_user_id: Optional[int] = None) -> bool:
        """Check if email address already exists (case-insensitive)."""
        logger.debug(f"Checking if email exists: {email}")
        normalized_email = email.lower().strip()
        conditions = [func.lower(self.model.email) == normalized_email]
        if exclude_user_id is not None:
            conditions.append(self.model.id != exclude_user_id)
        stmt = select(func.count(self.model.id)).where(and_(*conditions))
        result = await self.db_session.execute(stmt)
        count = result.scalar_one() or 0
        exists = count > 0
        logger.debug(f"Email exists check for {email}: {exists}")
        return exists

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def get_by_name(self, name: str) -> Optional[User]:
        """Get user by name."""
        logger.debug(f"Retrieving user by name: {name}")
        stmt = select(self.model).where(
            and_(
                self.model.name == name,
                self.model.is_active == True,
            )
        )
        result = await self.db_session.execute(stmt)
        user = result.scalar_one_or_none()
        if user:
            logger.debug(f"User found for name: {name}")
        else:
            logger.debug(f"No user found for name: {name}")
        return user

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def get_active_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """Get all active users."""
        logger.debug(f"Retrieving active users: skip={skip}, limit={limit}")
        stmt = (
            select(self.model).where(self.model.is_active == True).offset(skip).limit(limit).order_by(self.model.name)
        )
        result = await self.db_session.execute(stmt)
        users = list(result.scalars().all())
        logger.debug(f"Retrieved {len(users)} active users")
        return users

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def search_users(self, search_term: str, skip: int = 0, limit: int = 100) -> List[User]:
        """Search users by name or email."""
        logger.debug(f"Searching users with term: {search_term}, skip={skip}, limit={limit}")
        search_pattern = f"%{search_term}%"
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_active == True,
                    or_(
                        self.model.name.ilike(search_pattern),
                        self.model.email.ilike(search_pattern),
                    ),
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        result = await self.db_session.execute(stmt)
        users = list(result.scalars().all())
        logger.debug(f"Found {len(users)} users matching search term: {search_term}")
        return users

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def count_active_users(self) -> int:
        """Count total number of active users."""
        logger.debug("Counting active users")
        stmt = select(func.count(self.model.id)).where(self.model.is_active == True)
        result = await self.db_session.execute(stmt)
        count = result.scalar_one() or 0
        logger.debug(f"Total active users count: {count}")
        return count

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def update_password(self, user_id: int, password_hash: str) -> bool:
        """Update user password hash."""
        logger.debug(f"Updating password for user {user_id}")
        stmt = update(self.model).where(self.model.id == user_id).values(password_hash=password_hash)
        result = await self.db_session.execute(stmt)
        if result.rowcount > 0:
            logger.debug(f"Password updated for user {user_id}")
            return True
        logger.debug(f"User {user_id} not found for password update")
        return False

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def deactivate_user(self, user_id: int) -> bool:
        """Deactivate a user account."""
        logger.debug(f"Deactivating user {user_id}")
        stmt = update(self.model).where(self.model.id == user_id).values(is_active=False)
        result = await self.db_session.execute(stmt)
        if result.rowcount > 0:
            logger.debug(f"User {user_id} deactivated successfully")
            return True
        logger.debug(f"User {user_id} not found for deactivation")
        return False

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def activate_user(self, user_id: int) -> bool:
        """Activate a user account."""
        logger.debug(f"Activating user {user_id}")
        stmt = update(self.model).where(self.model.id == user_id).values(is_active=True)
        result = await self.db_session.execute(stmt)
        if result.rowcount > 0:
            logger.debug(f"User {user_id} activated successfully")
            return True
        logger.debug(f"User {user_id} not found for activation")
        return False

    @handle_repository_errors("user")
    @monitor_repository_performance("user")
    async def soft_delete_user(self, user_id: int, deleted_by_user_id: Optional[int] = None) -> bool:
        """Soft delete a user account."""
        logger.debug(f"Soft deleting user {user_id}")

        from src.core.utils.datetime_utils import utcnow_naive

        update_data = {
            "is_deleted": True,
            "deleted_at": utcnow_naive(),
        }
        if deleted_by_user_id:
            update_data["deleted_by_user_id"] = deleted_by_user_id

        stmt = (
            update(self.model)
            .where(and_(self.model.id == user_id, self.model.is_deleted == False))
            .values(**update_data)
        )
        result = await self.db_session.execute(stmt)
        if result.rowcount > 0:
            logger.debug(f"User {user_id} soft deleted successfully")
            return True
        logger.debug(f"User {user_id} not found for soft deletion")
        return False
