"""Intelligent Validation Caching System with Invalidation.

This module provides advanced caching for validation results with intelligent invalidation,
dependency tracking, and performance optimization for electrical engineering validation.
"""

import asyncio
import hashlib
import json
import re
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
import threading
import time
from collections import defaultdict, OrderedDict
import weakref

from src.config.logging_config import logger


class CacheStrategy(Enum):
    """Caching strategies available."""

    LRU = "lru"
    LFU = "lfu"
    TTL = "ttl"
    HYBRID = "hybrid"
    PERSISTENT = "persistent"


class InvalidationStrategy(Enum):
    """Cache invalidation strategies."""

    IMMEDIATE = "immediate"
    LAZY = "lazy"
    TIME_BASED = "time_based"
    DEPENDENCY_BASED = "dependency_based"
    EVENT_BASED = "event_based"


class CacheEntryStatus(Enum):
    """Status of cache entries."""

    VALID = "valid"
    INVALID = "invalid"
    EXPIRED = "expired"
    DEPENDENCY_CHANGED = "dependency_changed"


@dataclass
class CacheKey:
    """Cache key with metadata for validation caching."""

    primary_key: str
    validation_type: str
    parameters_hash: str
    created_at: datetime
    dependencies: Set[str]
    entity_ids: Set[str]
    regional_context: Optional[str] = None

    def __hash__(self) -> int:
        return hash((self.primary_key, self.validation_type, self.parameters_hash))


@dataclass
class CacheEntry:
    """Cache entry with validation result and metadata."""

    key: CacheKey
    result: Any
    status: CacheEntryStatus
    created_at: datetime
    accessed_at: datetime
    access_count: int
    size_bytes: int
    dependencies: Set[str]
    invalidation_triggers: List[str]
    validation_score: float

    def is_expired(self, ttl_seconds: int) -> bool:
        """Check if the entry has expired."""
        return (datetime.utcnow() - self.created_at).total_seconds() > ttl_seconds


@dataclass
class CacheStatistics:
    """Cache performance statistics."""

    total_entries: int
    valid_entries: int
    invalid_entries: int
    cache_hits: int
    cache_misses: int
    memory_usage_bytes: int
    hit_ratio: float
    average_response_time_ms: float
    eviction_count: int
    invalidation_count: int


@dataclass
class CacheInvalidationEvent:
    """Cache invalidation event."""

    event_type: str
    entity_id: str
    timestamp: datetime
    affected_keys: List[str]
    reason: str
    metadata: Dict[str, Any]


class IntelligentValidationCache:
    """Advanced cache system for validation results with intelligent invalidation."""

    def __init__(
        self,
        max_size_mb: int = 100,
        default_ttl_seconds: int = 3600,
        strategy: CacheStrategy = CacheStrategy.HYBRID,
        invalidation_strategy: InvalidationStrategy = InvalidationStrategy.DEPENDENCY_BASED,
    ):
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.default_ttl_seconds = default_ttl_seconds
        self.strategy = strategy
        self.invalidation_strategy = invalidation_strategy

        # Cache storage
        self.cache: Dict[str, CacheEntry] = {}
        self.dependency_map: Dict[str, Set[str]] = defaultdict(set)
        self.entity_dependency_map: Dict[str, Set[str]] = defaultdict(set)

        # Performance tracking
        self.cache_hits = 0
        self.cache_misses = 0
        self.eviction_count = 0
        self.invalidation_count = 0
        self.response_times: List[float] = []

        # Thread safety
        self._lock = threading.RLock()

        # LRU/LFU tracking
        self.access_order: OrderedDict[str, datetime] = OrderedDict()
        self.access_frequency: Dict[str, int] = defaultdict(int)

        # Event logging
        self.invalidation_events: List[CacheInvalidationEvent] = []

        # Cleanup scheduling
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = datetime.utcnow()

        logger.info(f"Initialized IntelligentValidationCache with strategy: {strategy.value}")

    async def get(
        self,
        key: CacheKey,
        validator_func: Optional[Callable[..., Any]] = None,
        force_refresh: bool = False,
    ) -> Optional[Any]:
        """Get validation result from cache with optional refresh."""
        start_time = time.time()

        with self._lock:
            cache_key_str = self._generate_cache_key_str(key)

            # Check if entry exists
            if cache_key_str not in self.cache:
                self.cache_misses += 1
                return None

            entry = self.cache[cache_key_str]

            # Check if entry is valid
            if not force_refresh and self._is_entry_valid(entry):
                # Update access tracking
                entry.accessed_at = datetime.utcnow()
                entry.access_count += 1

                # Update LRU/LFU tracking
                self.access_order[cache_key_str] = datetime.utcnow()
                self.access_frequency[cache_key_str] += 1

                self.cache_hits += 1

                response_time = (time.time() - start_time) * 1000
                self.response_times.append(response_time)

                logger.debug(f"Cache hit for key: {cache_key_str}")
                return entry.result

            # Entry is invalid or expired
            if force_refresh or entry.is_expired(self.default_ttl_seconds):
                await self._invalidate_entry(cache_key_str, "expired_or_forced")
                self.cache_misses += 1
                return None

            # Validate with validator function if provided
            if validator_func:
                is_valid = await validator_func(entry.result)
                if not is_valid:
                    await self._invalidate_entry(cache_key_str, "validator_failed")
                    self.cache_misses += 1
                    return None

            self.cache_misses += 1
            return None

    async def set(
        self,
        key: CacheKey,
        result: Any,
        ttl_seconds: Optional[int] = None,
        validation_score: float = 1.0,
    ) -> None:
        """Store validation result in cache."""
        with self._lock:
            cache_key_str = self._generate_cache_key_str(key)

            # Calculate entry size
            entry_size = self._calculate_entry_size(result)

            # Check if we need to evict entries
            current_size = self._get_current_size()
            if current_size + entry_size > self.max_size_bytes:
                await self._evict_entries(current_size + entry_size - self.max_size_bytes)

            # Create cache entry
            entry = CacheEntry(
                key=key,
                result=result,
                status=CacheEntryStatus.VALID,
                created_at=datetime.utcnow(),
                accessed_at=datetime.utcnow(),
                access_count=1,
                size_bytes=entry_size,
                dependencies=key.dependencies,
                invalidation_triggers=[],
                validation_score=validation_score,
            )

            # Store entry
            self.cache[cache_key_str] = entry

            # Update dependency maps
            for dep in key.dependencies:
                self.dependency_map[dep].add(cache_key_str)

            for entity_id in key.entity_ids:
                self.entity_dependency_map[entity_id].add(cache_key_str)

            # Update tracking
            self.access_order[cache_key_str] = datetime.utcnow()
            self.access_frequency[cache_key_str] = 1

            logger.debug(f"Cached entry: {cache_key_str} (size: {entry_size} bytes)")

    async def invalidate_by_entity(self, entity_id: str, reason: str = "entity_updated") -> None:
        """Invalidate all cache entries related to an entity."""
        with self._lock:
            affected_keys = list(self.entity_dependency_map.get(entity_id, set()))

            for key in affected_keys:
                await self._invalidate_entry(key, reason, entity_id)

            if affected_keys:
                self.invalidation_count += len(affected_keys)
                self._log_invalidation_event("entity_invalidation", entity_id, affected_keys, reason)

    async def invalidate_by_dependency(self, dependency: str, reason: str = "dependency_changed") -> None:
        """Invalidate all cache entries dependent on a specific dependency."""
        with self._lock:
            affected_keys = list(self.dependency_map.get(dependency, set()))

            for key in affected_keys:
                await self._invalidate_entry(key, reason, dependency)

            if affected_keys:
                self.invalidation_count += len(affected_keys)
                self._log_invalidation_event("dependency_invalidation", dependency, affected_keys, reason)

    async def invalidate_by_pattern(self, pattern: str, reason: str = "pattern_match") -> None:
        """Invalidate cache entries matching a pattern."""
        with self._lock:
            affected_keys = []
            pattern_regex = re.compile(pattern)

            for key_str in list(self.cache.keys()):
                if pattern_regex.search(key_str):
                    affected_keys.append(key_str)
                    await self._invalidate_entry(key_str, reason)

            if affected_keys:
                self.invalidation_count += len(affected_keys)
                self._log_invalidation_event("pattern_invalidation", pattern, affected_keys, reason)

    async def invalidate_all(self, reason: str = "global_invalidation") -> None:
        """Invalidate all cache entries."""
        with self._lock:
            affected_keys = list(self.cache.keys())

            for key in affected_keys:
                await self._invalidate_entry(key, reason)

            self.invalidation_count += len(affected_keys)
            self._log_invalidation_event("global_invalidation", "all", affected_keys, reason)

    async def _invalidate_entry(self, cache_key_str: str, reason: str, source: Optional[str] = None) -> None:
        """Invalidate a specific cache entry."""
        if cache_key_str in self.cache:
            entry = self.cache[cache_key_str]
            entry.status = CacheEntryStatus.INVALID

            # Clean up dependency maps
            for dep in entry.dependencies:
                if dep in self.dependency_map:
                    self.dependency_map[dep].discard(cache_key_str)
                    if not self.dependency_map[dep]:
                        del self.dependency_map[dep]

            for entity_id in entry.key.entity_ids:
                if entity_id in self.entity_dependency_map:
                    self.entity_dependency_map[entity_id].discard(cache_key_str)
                    if not self.entity_dependency_map[entity_id]:
                        del self.entity_dependency_map[entity_id]

            # Clean up tracking
            if cache_key_str in self.access_order:
                del self.access_order[cache_key_str]
            if cache_key_str in self.access_frequency:
                del self.access_frequency[cache_key_str]

            # Remove from cache
            del self.cache[cache_key_str]

            logger.debug(f"Invalidated cache entry: {cache_key_str} (reason: {reason})")

    async def _evict_entries(self, required_space: int) -> None:
        """Evict entries based on cache strategy."""
        if not self.cache:
            return

        evicted_count = 0

        if self.strategy == CacheStrategy.LRU:
            evicted_count = await self._evict_lru(required_space)
        elif self.strategy == CacheStrategy.LFU:
            evicted_count = await self._evict_lfu(required_space)
        elif self.strategy == CacheStrategy.TTL:
            evicted_count = await self._evict_ttl(required_space)
        elif self.strategy == CacheStrategy.HYBRID:
            evicted_count = await self._evict_hybrid(required_space)

        self.eviction_count += evicted_count

    async def _evict_lru(self, required_space: int) -> int:
        """Evict least recently used entries."""
        evicted_count = 0
        freed_space = 0

        # Sort by last access time (oldest first)
        sorted_keys = sorted(self.access_order.items(), key=lambda x: x[1])

        for key_str, _ in sorted_keys:
            if freed_space >= required_space:
                break

            entry = self.cache[key_str]
            freed_space += entry.size_bytes
            await self._invalidate_entry(key_str, "lru_eviction")
            evicted_count += 1

        return evicted_count

    async def _evict_lfu(self, required_space: int) -> int:
        """Evict least frequently used entries."""
        evicted_count = 0
        freed_space = 0

        # Sort by access frequency (lowest first)
        sorted_keys = sorted(self.access_frequency.items(), key=lambda x: x[1])

        for key_str, _ in sorted_keys:
            if freed_space >= required_space:
                break

            entry = self.cache[key_str]
            freed_space += entry.size_bytes
            await self._invalidate_entry(key_str, "lfu_eviction")
            evicted_count += 1

        return evicted_count

    async def _evict_ttl(self, required_space: int) -> int:
        """Evict expired entries."""
        evicted_count = 0
        freed_space = 0

        expired_keys = [key_str for key_str, entry in self.cache.items() if entry.is_expired(self.default_ttl_seconds)]

        for key_str in expired_keys:
            if freed_space >= required_space:
                break

            entry = self.cache[key_str]
            freed_space += entry.size_bytes
            await self._invalidate_entry(key_str, "ttl_expiration")
            evicted_count += 1

        return evicted_count

    async def _evict_hybrid(self, required_space: int) -> int:
        """Evict using hybrid strategy (TTL first, then LRU/LFU)."""
        evicted_count = 0
        freed_space = 0

        # First, evict expired entries
        evicted_count += await self._evict_ttl(required_space - freed_space)

        # Then, evict least recently used
        if freed_space < required_space:
            evicted_count += await self._evict_lru(required_space - freed_space)

        return evicted_count

    def _is_entry_valid(self, entry: CacheEntry) -> bool:
        """Check if a cache entry is valid."""
        if entry.status != CacheEntryStatus.VALID:
            return False

        if entry.is_expired(self.default_ttl_seconds):
            return False

        return True

    def _generate_cache_key_str(self, key: CacheKey) -> str:
        """Generate string representation of cache key."""
        key_data = {
            "primary_key": key.primary_key,
            "validation_type": key.validation_type,
            "parameters_hash": key.parameters_hash,
            "regional_context": key.regional_context,
            "dependencies": sorted(key.dependencies),
            "entity_ids": sorted(key.entity_ids),
        }
        return hashlib.md5(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

    def _calculate_entry_size(self, result: Any) -> int:
        """Calculate approximate size of cache entry in bytes."""
        try:
            return len(json.dumps(result, default=str).encode("utf-8"))
        except:
            return 1024  # Default size estimate

    def _get_current_size(self) -> int:
        """Get current cache size in bytes."""
        return sum(entry.size_bytes for entry in self.cache.values())

    def _log_invalidation_event(self, event_type: str, source: str, affected_keys: List[str], reason: str) -> None:
        """Log cache invalidation event."""
        event = CacheInvalidationEvent(
            event_type=event_type,
            entity_id=source,
            timestamp=datetime.utcnow(),
            affected_keys=affected_keys,
            reason=reason,
            metadata={
                "affected_keys_count": len(affected_keys),
                "cache_size": len(self.cache),
            },
        )

        self.invalidation_events.append(event)
        logger.info(f"Cache invalidation: {event_type} - {len(affected_keys)} keys affected")

    async def get_statistics(self) -> CacheStatistics:
        """Get cache performance statistics."""
        total_requests = self.cache_hits + self.cache_misses
        hit_ratio = self.cache_hits / total_requests if total_requests > 0 else 0.0

        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0.0

        valid_entries = sum(1 for entry in self.cache.values() if self._is_entry_valid(entry))
        invalid_entries = len(self.cache) - valid_entries

        return CacheStatistics(
            total_entries=len(self.cache),
            valid_entries=valid_entries,
            invalid_entries=invalid_entries,
            cache_hits=self.cache_hits,
            cache_misses=self.cache_misses,
            memory_usage_bytes=self._get_current_size(),
            hit_ratio=hit_ratio,
            average_response_time_ms=avg_response_time,
            eviction_count=self.eviction_count,
            invalidation_count=self.invalidation_count,
        )

    async def cleanup_expired(self) -> None:
        """Clean up expired cache entries."""
        with self._lock:
            expired_keys = [
                key_str for key_str, entry in self.cache.items() if entry.is_expired(self.default_ttl_seconds)
            ]

            for key_str in expired_keys:
                await self._invalidate_entry(key_str, "cleanup_expired")

            if expired_keys:
                logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

            self.last_cleanup = datetime.utcnow()

    async def get_invalidation_history(
        self, limit: int = 100, event_type: Optional[str] = None
    ) -> List[CacheInvalidationEvent]:
        """Get cache invalidation history."""
        events = self.invalidation_events

        if event_type:
            events = [e for e in events if e.event_type == event_type]

        return events[-limit:]

    def reset_statistics(self) -> None:
        """Reset cache performance statistics."""
        with self._lock:
            self.cache_hits = 0
            self.cache_misses = 0
            self.eviction_count = 0
            self.invalidation_count = 0
            self.response_times.clear()

    async def preload_cache(self, preload_data: List[Tuple[CacheKey, Any]]) -> None:
        """Preload cache with validation results."""
        for key, result in preload_data:
            await self.set(key, result)

    async def export_cache_state(self) -> Dict[str, Any]:
        """Export current cache state for backup/restore."""
        with self._lock:
            return {
                "entries": [
                    {
                        "key": asdict(entry.key),
                        "result": entry.result,
                        "created_at": entry.created_at.isoformat(),
                        "access_count": entry.access_count,
                        "validation_score": entry.validation_score,
                    }
                    for entry in self.cache.values()
                    if self._is_entry_valid(entry)
                ],
                "statistics": asdict(await self.get_statistics()),
                "strategy": self.strategy.value,
                "invalidation_strategy": self.invalidation_strategy.value,
            }

    async def import_cache_state(self, state: Dict[str, Any]) -> None:
        """Import cache state from backup."""
        with self._lock:
            # Clear existing cache
            await self.invalidate_all("import_reset")

            # Import entries
            for entry_data in state.get("entries", []):
                key = CacheKey(**entry_data["key"])
                result = entry_data["result"]

                await self.set(key, result)

            logger.info(f"Imported {len(state.get('entries', []))} cache entries")


# Global cache instance
validation_cache = IntelligentValidationCache()
