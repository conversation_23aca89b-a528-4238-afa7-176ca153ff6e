/**
 * Authentication and user management types
 * 
 * These types correspond to the backend schemas defined in:
 * server/src/core/schemas/general/user_schemas.py
 */

// Base user information
export interface UserRead {
  id: number
  name: string
  email: string
  is_superuser: boolean
  is_active: boolean
  role?: string | null
  last_login?: string | null
  created_at: string
  updated_at: string
}

export interface UserCreate {
  name: string
  email: string
  password: string
  is_superuser?: boolean
}

export interface UserUpdate {
  name?: string
  email?: string
  password?: string
}

export interface UserSummary {
  id: number
  name: string
  email: string
  is_active: boolean
  role?: string | null
  last_login?: string | null
  created_at: string
}

// Authentication request/response types
export interface LoginRequest {
  username: string // Can be username or email
  password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: UserRead
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  confirm_password: string
  // Professional credentials for electrical designers
  professional_license?: string
  company_name?: string
  job_title?: string
  years_experience?: number
  specializations?: string[]
}

export interface RegisterResponse {
  message: string
  user: UserRead
  requires_verification?: boolean
}

export interface LogoutResponse {
  message: string
  logged_out_at: string
}

// Password management types
export interface PasswordChangeRequest {
  current_password: string
  new_password: string
  confirm_password: string
}

export interface PasswordChangeResponse {
  message: string
  changed_at: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetResponse {
  message: string
  reset_token_sent: boolean
  expires_at?: string | null
}

export interface PasswordResetConfirm {
  token: string
  new_password: string
  confirm_password: string
}

// Auth store state types
export interface AuthState {
  user: UserRead | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export interface AuthActions {
  setAuth: (user: UserRead, token: string) => void
  updateUser: (user: UserRead) => void
  clearAuth: () => void
  setLoading: (loading: boolean) => void
  initializeAuth: () => void
}

// Form validation types
export interface LoginFormData {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterFormData {
  name: string
  email: string
  password: string
  confirm_password: string
  professional_license?: string
  company_name?: string
  job_title?: string
  years_experience?: number
  specializations?: string[]
  terms_accepted: boolean
}

export interface FormFieldError {
  field: string
  message: string
}

export interface ValidationErrors {
  [key: string]: string | string[]
}

// API error types
export interface AuthError {
  message: string
  code: string
  details?: Record<string, unknown>
}

// Route protection types
export interface ProtectedRouteProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  requireAuth?: boolean
  requiredRoles?: string[]
}

// Password complexity requirements
export interface PasswordRequirements {
  minLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSymbols: boolean
  forbidCommon: boolean
}

export const DEFAULT_PASSWORD_REQUIREMENTS: PasswordRequirements = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSymbols: true,
  forbidCommon: true,
}

// Session management types
export interface UserSession {
  id: string
  user_id: number
  ip_address?: string | null
  user_agent?: string | null
  created_at: string
  last_activity: string
  expires_at: string
  is_active: boolean
}

// Activity tracking types
export interface UserActivity {
  id: number
  user_id: number
  action: string
  resource?: string | null
  ip_address?: string | null
  user_agent?: string | null
  timestamp: string
  success: boolean
  details?: Record<string, unknown> | null
}

// Professional credentials for electrical designers
export interface ProfessionalCredentials {
  license_number?: string
  license_type?: string
  license_state?: string
  license_expiry?: string
  certifications?: string[]
  professional_memberships?: string[]
}

// Enhanced registration data with professional info
export interface ProfessionalRegistrationData extends RegisterFormData {
  professional_credentials?: ProfessionalCredentials
}