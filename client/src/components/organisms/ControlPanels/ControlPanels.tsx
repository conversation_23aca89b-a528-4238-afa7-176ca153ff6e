/**
 * Control Panels Organism
 *
 * Professional electrical system control interface with safety interlocks.
 * Provides real-time control capabilities with IEEE/IEC safety compliance.
 *
 * Features:
 * - Professional electrical system controls
 * - Real-time safety interlock monitoring
 * - Multi-level permission system
 * - Emergency stop capabilities
 * - Control sequence execution
 * - WCAG 2.1 AA accessibility compliance
 * - Engineering-grade quality standards
 * - TypeScript strict mode compliance
 */

import React, { useCallback, useMemo } from "react"

import type {
  ControlAction,
  ControlAlert,
  ControlEquipment,
  ControlGroup,
  ControlPanelsProps,
  ControlState,
  ControlSystemType,
  SafetyInterlock,
} from "./ControlPanelsTypes"

import {
  Activity,
  AlertOctagon,
  AlertTriangle,
  Bell,
  ChevronDown,
  ChevronUp,
  Clock,
  Filter,
  Grid,
  List,
  Play,
  RefreshCw,@/components/atoms/input
  Search,
  Settings,@/components/atoms/card
  Shield,
  Square,
  Users,
  Zap,
} from "lucide-react"@/components/atoms/switch
import { ErrorBoundary @/components/atoms/labeloundary"

import { cn } from "@/lib/utils"

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui_LEGACY/alert-dialog"
import { Badge } from "@/components/ui_LEGACY/badge"
import { Card } from "@/components/ui_LEGACY/card"
import { Input } from "@/components/ui_LEGACY/input"
import { Label } from "@/components/ui_LEGACY/label"
import { Progress } from "@/components/ui_LEGACY/progress"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui_LEGACY/select"
import { Separator } from "@/components/ui_LEGACY/separator"
import { Switch } from "@/components/ui_LEGACY/switch"
import { Button } from "@/components/ui_LEGACY/button"
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui_LEGACY/sheet"
import { Skeleton } from "@/components/ui_LEGACY/skeleton"

import {
  canBypassInterlock,
  CONTROL_STATE_COLORS,
  CONTROL_SYSTEM_LABELS,
  hasActiveInterlocks,
  isActionPermitted,
  PERMISSION_LEVEL_LABELS,
  SAFETY_LEVEL_LABELS,
} from "./ControlPanelsTypes"
import { useControlPanels } from "./useControlPanels"

// Error Fallback Component
const ErrorFallback: React.FC<{
  error: Error
  resetErrorBoundary: () => void
}> = ({ error, resetErrorBoundary }) => (
  <Card className="p-6">
    <div className="mb-4 flex items-center gap-3 text-red-600">
      <AlertTriangle className="h-5 w-5" />
      <h3 className="font-semibold">Control Panels Error</h3>
    </div>
    <p className="text-muted-foreground mb-4 text-sm">
      {error.message || "An unexpected error occurred in the control panels."}
    </p>
    <Button onClick={resetErrorBoundary} variant="outline" size="sm">
      <RefreshCw className="mr-2 h-4 w-4" />
      Try Again
    </Button>
  </Card>
)

// Control Equipment Card Component
const ControlEquipmentCard: React.FC<{
  equipment: ControlEquipment
  selected?: boolean
  onSelect?: () => void
  onAction?: (action: ControlAction) => void
  showDetails?: boolean
  showInterlocks?: boolean
  size?: "sm" | "md" | "lg"
}> = ({
  equipment,
  selected = false,
  onSelect,
  onAction,
  showDetails = true,
  showInterlocks = true,
  size = "md",
}) => {
  const activeInterlocks = equipment.interlocks.filter((i) => i.active)
  const stateColor = CONTROL_STATE_COLORS[equipment.state]

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        selected && "ring-primary ring-2",
        !equipment.isOnline && "opacity-75",
        size === "sm" && "p-3",
        size === "md" && "p-4",
        size === "lg" && "p-6"
      )}
      onClick={onSelect}
    >
      <div className="mb-3 flex items-start justify-between">
        <div className="flex-1">
          <div className="mb-1 flex items-center gap-2">
            {equipment.icon && (
              <equipment.icon className="text-muted-foreground h-4 w-4" />
            )}
            <h4 className="text-sm font-medium">{equipment.name}</h4>
            {!equipment.isOnline && (
              <Badge variant="destructive" className="text-xs">
                Offline
              </Badge>
            )}
          </div>
          <p className="text-muted-foreground text-xs">
            {CONTROL_SYSTEM_LABELS[equipment.type]} • {equipment.location}
          </p>
        </div>

        <div className="flex flex-col items-end gap-1">
          <Badge
            variant={
              equipment.state === "running" || equipment.state === "automatic"
                ? "default"
                : "secondary"
            }
            className={cn("text-xs", stateColor)}
          >
            {equipment.state.replace("_", " ")}
          </Badge>
          {activeInterlocks.length > 0 && (
            <Badge variant="destructive" className="text-xs">
              <Shield className="mr-1 h-3 w-3" />
              {activeInterlocks.length}
            </Badge>
          )}
        </div>
      </div>

      {/* Current Values */}
      {showDetails && equipment.currentValue !== undefined && (
        <div className="mb-3 grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-muted-foreground">Current:</span>
            <span className="ml-1 font-mono">
              {equipment.currentValue}
              {equipment.unit}
            </span>
          </div>
          {equipment.setpoint !== undefined && (
            <div>
              <span className="text-muted-foreground">Setpoint:</span>
              <span className="ml-1 font-mono">
                {equipment.setpoint}
                {equipment.unit}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Progress Bar for Current/Max Values */}
      {showDetails &&
        equipment.currentValue !== undefined &&
        equipment.maxValue !== undefined && (
          <div className="mb-3">
            <Progress
              value={(equipment.currentValue / equipment.maxValue) * 100}
              className="h-2"
            />
          </div>
        )}

      {/* Active Interlocks */}
      {showInterlocks && activeInterlocks.length > 0 && (
        <div className="mb-3">
          <div className="mb-1 flex items-center gap-1 text-xs font-medium text-red-600">
            <AlertTriangle className="h-3 w-3" />
            Active Interlocks
          </div>
          <div className="space-y-1">
            {activeInterlocks.slice(0, 2).map((interlock) => (
              <div key={interlock.id} className="text-muted-foreground text-xs">
                • {interlock.name}
              </div>
            ))}
            {activeInterlocks.length > 2 && (
              <div className="text-muted-foreground text-xs">
                +{activeInterlocks.length - 2} more
              </div>
            )}
          </div>
        </div>
      )}

      {/* Control Actions */}
      {equipment.isControllable && equipment.availableActions.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {equipment.availableActions.slice(0, 3).map((action) => (
            <Button
              key={action}
              size="sm"
              variant="outline"
              className="h-7 px-2 text-xs"
              onClick={(e) => {
                e.stopPropagation()
                onAction?.(action)
              }}
              disabled={hasActiveInterlocks(equipment)}
            >
              {action === "start" && <Play className="mr-1 h-3 w-3" />}
              {action === "stop" && <Square className="mr-1 h-3 w-3" />}
              {action === "emergency_stop" && (
                <AlertOctagon className="mr-1 h-3 w-3" />
              )}
              {action.replace("_", " ")}
            </Button>
          ))}
        </div>
      )}
    </Card>
  )
}

// Control Group Card Component
const ControlGroupCard: React.FC<{
  group: ControlGroup
  expanded?: boolean
  onToggleExpand?: () => void
  onEquipmentSelect?: (equipmentId: string) => void
  onEquipmentAction?: (equipmentId: string, action: ControlAction) => void
  onGroupAction?: (action: ControlAction) => void
  showDetails?: boolean
  showInterlocks?: boolean
  size?: "sm" | "md" | "lg"
}> = ({
  group,
  expanded = false,
  onToggleExpand,
  onEquipmentSelect,
  onEquipmentAction,
  onGroupAction,
  showDetails = true,
  showInterlocks = true,
  size = "md",
}) => {
  const totalInterlocks =
    group.globalInterlocks.filter((i) => i.active).length +
    group.equipment.reduce(
      (acc, eq) => acc + eq.interlocks.filter((i) => i.active).length,
      0
    )

  const operationalCount = group.equipment.filter(
    (eq) => eq.state === "running" || eq.state === "automatic"
  ).length

  return (
    <Card className={cn("transition-all duration-200", size === "lg" && "p-6")}>
      {/* Group Header */}
      <div className="flex items-center justify-between p-4 pb-3">
        <div className="flex items-center gap-3">
          {group.icon && (
            <group.icon className="text-muted-foreground h-5 w-5" />
          )}
          <div>
            <h3 className="font-medium">{group.name}</h3>
            {group.description && (
              <p className="text-muted-foreground text-sm">
                {group.description}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Group Stats */}
          <Badge variant="outline" className="text-xs">
            {operationalCount}/{group.equipment.length} operational
          </Badge>

          {totalInterlocks > 0 && (
            <Badge variant="destructive" className="text-xs">
              <Shield className="mr-1 h-3 w-3" />
              {totalInterlocks}
            </Badge>
          )}

          {/* Emergency Stop */}
          {group.emergencyStop && (
            <Button
              size="sm"
              variant="destructive"
              onClick={() => onGroupAction?.("emergency_stop")}
            >
              <AlertOctagon className="mr-1 h-4 w-4" />
              E-Stop
            </Button>
          )}

          {/* Expand/Collapse */}
          <Button size="sm" variant="ghost" onClick={onToggleExpand}>
            {expanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Equipment Grid */}
      {expanded && (
        <div className="px-4 pb-4">
          <Separator className="mb-4" />
          <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3">
            {group.equipment.map((equipment) => (
              <ControlEquipmentCard
                key={equipment.id}
                equipment={equipment}
                onSelect={() => onEquipmentSelect?.(equipment.id)}
                onAction={(action) => onEquipmentAction?.(equipment.id, action)}
                showDetails={showDetails}
                showInterlocks={showInterlocks}
                size="sm"
              />
            ))}
          </div>
        </div>
      )}
    </Card>
  )
}

// Main Component
export const ControlPanels: React.FC<ControlPanelsProps> = ({
  className,
  "data-testid": testId,
  ...props
}) => {
  const {
    // Data
    controlGroups,
    filteredEquipment,
    alerts,
    activeSession,

    // State
    loading,
    error,
    layout,
    config,
    filters,
    selection,
    safety,
    panels,
    healthSummary,
    alertSummary,
    sessionSummary,
    hasUnacknowledgedAlerts,
    hasActiveInterlocks,
    connectionStatus,

    // Actions
    executeControlAction,
    executeBulkAction,
    executeGroupAction,
    activateEmergencyStop,
    acknowledgeAlert,
    acknowledgeAllAlerts,
    refreshData,

    // UI Actions
    updateLayout,
    updateConfig,
    setSearchQuery,
    toggleFilterPanel,
    selectEquipment,
    selectAllEquipment,
    clearSelection,
    toggleBulkActionMode,
  } = useControlPanels()

  // Group expansion state
  const [expandedGroups, setExpandedGroups] = React.useState<Set<string>>(
    new Set()
  )

  const toggleGroupExpansion = useCallback((groupId: string) => {
    setExpandedGroups((prev) => {
      const next = new Set(prev)
      if (next.has(groupId)) {
        next.delete(groupId)
      } else {
        next.add(groupId)
      }
      return next
    })
  }, [])

  // Handlers
  const handleEquipmentAction = useCallback(
    async (equipmentId: string, action: ControlAction) => {
      try {
        await executeControlAction(equipmentId, action)
      } catch (error) {
        // Error handling is done in the hook
      }
    },
    [executeControlAction]
  )

  const handleGroupAction = useCallback(
    async (groupId: string, action: ControlAction) => {
      try {
        await executeGroupAction(groupId, action)
      } catch (error) {
        // Error handling is done in the hook
      }
    },
    [executeGroupAction]
  )

  const handleEmergencyStop = useCallback(async () => {
    try {
      await activateEmergencyStop("User initiated emergency stop", "all")
    } catch (error) {
      // Error handling is done in the hook
    }
  }, [activateEmergencyStop])

  // Loading skeleton
  if (loading && controlGroups.length === 0) {
    return (
      <div className={cn("space-y-6", className)} data-testid={testId}>
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <div className="flex gap-2">
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-20" />
          </div>
        </div>

        {/* Grid Skeleton */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className={cn("space-y-6", className)} data-testid={testId}>
        <ErrorFallback
          error={new Error(error)}
          resetErrorBoundary={() => refreshData()}
        />
      </div>
    )
  }

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <div
        className={cn("space-y-6", className)}
        data-testid={testId || "control-panels"}
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Control Panels
            </h2>
            <div className="text-muted-foreground mt-1 flex items-center gap-4 text-sm">
              <span>{healthSummary.total} equipment units</span>
              <span>•</span>
              <span>{healthSummary.operational} operational</span>
              {hasActiveInterlocks && (
                <>
                  <span>•</span>
                  <span className="flex items-center gap-1 text-amber-600">
                    <Shield className="h-3 w-3" />
                    Active interlocks
                  </span>
                </>
              )}
              {safety.emergencyStopActive && (
                <>
                  <span>•</span>
                  <span className="flex items-center gap-1 font-medium text-red-600">
                    <AlertOctagon className="h-3 w-3" />
                    EMERGENCY STOP ACTIVE
                  </span>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Connection Status */}
            <div className="flex items-center gap-2 text-sm">
              <div
                className={cn(
                  "h-2 w-2 rounded-full",
                  connectionStatus === "connected"
                    ? "bg-green-500"
                    : connectionStatus === "reconnecting"
                      ? "animate-pulse bg-yellow-500"
                      : "bg-red-500"
                )}
              />
              <span className="text-muted-foreground capitalize">
                {connectionStatus}
              </span>
            </div>

            {/* Alerts */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" className="relative">
                  <Bell className="mr-2 h-4 w-4" />
                  Alerts
                  {hasUnacknowledgedAlerts && (
                    <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500" />
                  )}
                  {alertSummary.total > 0 && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {alertSummary.total}
                    </Badge>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Active Alerts</SheetTitle>
                </SheetHeader>
                <div className="mt-6 space-y-4">
                  {alerts.length === 0 ? (
                    <p className="text-muted-foreground text-sm">
                      No active alerts
                    </p>
                  ) : (
                    <>
                      <div className="flex justify-between">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => acknowledgeAllAlerts()}
                        >
                          Mark All Read
                        </Button>
                      </div>
                      <div className="space-y-3">
                        {alerts.map((alert) => (
                          <Card key={alert.id} className="p-3">
                            <div className="mb-2 flex items-start justify-between">
                              <Badge
                                variant={
                                  alert.severity === "critical"
                                    ? "destructive"
                                    : "secondary"
                                }
                              >
                                {alert.severity}
                              </Badge>
                              {!alert.acknowledged && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => acknowledgeAlert(alert.id)}
                                >
                                  Acknowledge
                                </Button>
                              )}
                            </div>
                            <h4 className="text-sm font-medium">
                              {alert.title}
                            </h4>
                            <p className="text-muted-foreground mb-2 text-xs">
                              {alert.message}
                            </p>
                            <p className="text-muted-foreground text-xs">
                              {alert.equipmentName} •{" "}
                              {alert.timestamp.toLocaleTimeString()}
                            </p>
                          </Card>
                        ))}
                      </div>
                    </>
                  )}
                </div>
              </SheetContent>
            </Sheet>

            {/* Emergency Stop Button */}
            <AlertDialog>
              <AlertDialog.Trigger asChild>
                <Button
                  variant="destructive"
                  size="sm"
                  disabled={safety.emergencyStopActive}
                >
                  <AlertOctagon className="mr-2 h-4 w-4" />
                  Emergency Stop
                </Button>
              </AlertDialog.Trigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    Emergency Stop Confirmation
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    This will immediately stop ALL controlled equipment. This
                    action should only be used in emergency situations. Are you
                    sure you want to activate the emergency stop?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleEmergencyStop}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Activate Emergency Stop
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            {/* Refresh */}
            <Button variant="outline" size="sm" onClick={refreshData}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4">
          <div className="max-w-sm flex-1">
            <Input
              placeholder="Search control equipment..."
              value={filters.searchQuery || ""}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full"
            />
          </div>

          <Sheet
            open={panels.isFilterPanelOpen}
            onOpenChange={toggleFilterPanel}
          >
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filters
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Filter Controls</SheetTitle>
              </SheetHeader>
              <div className="mt-6 space-y-4">
                <p className="text-muted-foreground text-sm">
                  Filter options will be implemented in the next development
                  phase.
                </p>
              </div>
            </SheetContent>
          </Sheet>

          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </Button>
            </SheetTrigger>
            <SheetContent>
              <SheetHeader>
                <SheetTitle>Panel Configuration</SheetTitle>
              </SheetHeader>
              <div className="mt-6 space-y-6">
                <div className="space-y-3">
                  <Label>Layout</Label>
                  <Select
                    value={layout.columns.toString()}
                    onValueChange={(value) =>
                      updateLayout({
                        columns: parseInt(value) as 1 | 2 | 3 | 4,
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Column</SelectItem>
                      <SelectItem value="2">2 Columns</SelectItem>
                      <SelectItem value="3">3 Columns</SelectItem>
                      <SelectItem value="4">4 Columns</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-details">Show Details</Label>
                  <Switch
                    id="show-details"
                    checked={layout.showDetails}
                    onCheckedChange={(checked) =>
                      updateLayout({ showDetails: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="show-interlocks">Show Interlocks</Label>
                  <Switch
                    id="show-interlocks"
                    checked={layout.showInterlocks}
                    onCheckedChange={(checked) =>
                      updateLayout({ showInterlocks: checked })
                    }
                  />
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>

        {/* Session Info */}
        {sessionSummary.isActive && activeSession && (
          <Card className="border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900 dark:text-blue-100">
                    Control Session Active
                  </p>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    User: {activeSession.userName} • Permission:{" "}
                    {PERMISSION_LEVEL_LABELS[activeSession.permissionLevel]}
                  </p>
                </div>
              </div>
              {sessionSummary.timeRemaining && (
                <div className="flex items-center gap-2 text-blue-600">
                  <Clock className="h-4 w-4" />
                  <span className="font-mono text-sm">
                    {Math.floor(sessionSummary.timeRemaining / 60000)}m
                    remaining
                  </span>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* Control Groups */}
        {controlGroups.length === 0 ? (
          <Card className="p-8 text-center">
            <Activity className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-medium">
              No Control Groups Found
            </h3>
            <p className="text-muted-foreground">
              No control equipment is currently configured or available.
            </p>
          </Card>
        ) : (
          <div
            className={cn(
              "grid gap-6",
              layout.columns === 1 && "grid-cols-1",
              layout.columns === 2 && "grid-cols-1 lg:grid-cols-2",
              layout.columns === 3 &&
                "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
              layout.columns === 4 &&
                "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
            )}
          >
            {controlGroups.map((group) => (
              <ControlGroupCard
                key={group.id}
                group={group}
                expanded={expandedGroups.has(group.id)}
                onToggleExpand={() => toggleGroupExpansion(group.id)}
                onEquipmentSelect={selectEquipment}
                onEquipmentAction={handleEquipmentAction}
                onGroupAction={(action) => handleGroupAction(group.id, action)}
                showDetails={layout.showDetails}
                showInterlocks={layout.showInterlocks}
                size={layout.groupSize}
              />
            ))}
          </div>
        )}
      </div>
    </ErrorBoundary>
  )
}

ControlPanels.displayName = "ControlPanels"
