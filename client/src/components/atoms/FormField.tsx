/**
 * FormField Atom - Universal Foundational Component
 *
 * Enhanced atomic design form field component providing comprehensive form functionality
 * with validation states, accessibility, and engineering-grade quality.
 *
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Comprehensive validation states with contextual messaging
 * - Professional form field types and interactive features
 * - Performance optimized with conditional rendering
 * - Full backward compatibility support
 * - Icon integration and advanced field functionality
 */

import React, { forwardRef, useId, useState } from "react"

import type { VariantProps } from "class-variance-authority"

import { cva } from "class-variance-authority"
import {
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  HelpCircle,
  Info,
  Search,
  X,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Textarea } from "@/components/ui_LEGACY/textarea"

import { Input } from "../ui/Input"
import { Label } from "../ui/Label"

// Form field variants using CVA
const formFieldVariants = cva(
  "relative transition-colors focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 rounded-md",
  {
    variants: {
      size: {
        sm: "text-sm",
        default: "text-sm",
        lg: "text-base",
      },
      state: {
        default: "",
        error: "focus-within:ring-destructive",
        warning: "focus-within:ring-yellow-400",
        success: "focus-within:ring-green-400",
        info: "focus-within:ring-blue-400",
      },
    },
    defaultVariants: {
      size: "default",
      state: "default",
    },
  }
)

// Size configuration
const sizeConfig = {
  sm: {
    input: "h-8 text-sm",
    textarea: "text-sm min-h-[80px]",
    label: "text-sm",
    message: "text-xs",
    icon: "h-3 w-3",
  },
  default: {
    input: "h-10 text-sm",
    textarea: "text-sm min-h-[100px]",
    label: "text-sm",
    message: "text-sm",
    icon: "h-4 w-4",
  },
  lg: {
    input: "h-12 text-base",
    textarea: "text-base min-h-[120px]",
    label: "text-base",
    message: "text-sm",
    icon: "h-5 w-5",
  },
} as const

// Field types
export type FieldType =
  | "text"
  | "email"
  | "password"
  | "number"
  | "tel"
  | "url"
  | "search"
  | "textarea"
  | "hidden"

// Validation state types
export type ValidationState =
  | "default"
  | "error"
  | "warning"
  | "success"
  | "info"

export interface FormFieldProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement>,
    "size"
  > {
  // Core props
  /** Field label text */
  label: string
  /** Input field type */
  type?: FieldType

  // Validation props
  /** Error message(s) */
  error?: string | string[]
  /** Warning message(s) */
  warning?: string | string[]
  /** Success message(s) */
  success?: string | string[]
  /** Info message(s) */
  info?: string | string[]
  /** Help text */
  helpText?: string

  // State props
  /** Required field indicator */
  required?: boolean
  /** Optional field indicator */
  optional?: boolean
  /** Field has been interacted with */
  touched?: boolean
  /** Loading state */
  loading?: boolean

  // Appearance props
  /** Field size */
  size?: keyof typeof sizeConfig
  /** Field variant */
  variant?: "default" | "ghost" | "outline"

  // Interactive props
  /** Show clear button for text fields */
  clearable?: boolean
  /** Show password visibility toggle */
  showPasswordToggle?: boolean
  /** Clear field callback */
  onClear?: () => void

  // Additional props
  /** Field description */
  description?: string
  /** Tooltip text */
  tooltip?: string
  /** Prefix content */
  prefix?: React.ReactNode
  /** Suffix content */
  suffix?: React.ReactNode

  // Accessibility props
  /** ARIA described by */
  "aria-describedby"?: string
  /** Test ID for testing */
  "data-testid"?: string
}

export const FormField = forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  FormFieldProps
>(
  (
    {
      // Core props
      label,
      type = "text",

      // Validation props
      error,
      warning,
      success,
      info,
      helpText,

      // State props
      required = false,
      optional = false,
      touched = false,
      loading = false,

      // Appearance props
      size = "default",
      variant = "default",

      // Interactive props
      clearable = false,
      showPasswordToggle = false,
      onClear,

      // Additional props
      description,
      tooltip,
      prefix,
      suffix,

      // Standard props
      className,
      id: providedId,
      value,
      onChange,
      onFocus,
      onBlur,
      ...props
    },
    ref
  ) => {
    const generatedId = useId()
    const id = providedId || generatedId
    const sizeStyles = sizeConfig[size]

    // Password visibility state
    const [showPassword, setShowPassword] = useState(false)

    // Determine validation state
    const validationState: ValidationState = touched
      ? error
        ? "error"
        : warning
          ? "warning"
          : success
            ? "success"
            : info
              ? "info"
              : "default"
      : "default"

    // Normalize messages to arrays
    const normalizeMessages = (
      msg: string | string[] | undefined
    ): string[] => {
      if (!msg) return []
      return Array.isArray(msg) ? msg : [msg]
    }

    const errorMessages = normalizeMessages(error)
    const warningMessages = normalizeMessages(warning)
    const successMessages = normalizeMessages(success)
    const infoMessages = normalizeMessages(info)

    const hasMessages =
      errorMessages.length > 0 ||
      warningMessages.length > 0 ||
      successMessages.length > 0 ||
      infoMessages.length > 0

    // Handle clear functionality
    const handleClear = (e: React.MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()
      if (onClear) {
        onClear()
      } else if (onChange) {
        // Create synthetic event for controlled components
        const syntheticEvent = {
          target: { value: "" },
          currentTarget: { value: "" },
        } as React.ChangeEvent<HTMLInputElement>
        onChange(syntheticEvent)
      }
    }

    // Toggle password visibility
    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
    }

    // Determine actual input type
    const actualType = type === "password" && showPassword ? "text" : type

    // Generate IDs for accessibility
    const descriptionId = description ? `${id}-description` : undefined
    const helpTextId = helpText ? `${id}-help` : undefined
    const messagesId = hasMessages ? `${id}-messages` : undefined

    // Build aria-describedby
    const ariaDescribedBy =
      [descriptionId, helpTextId, messagesId, props["aria-describedby"]]
        .filter(Boolean)
        .join(" ") || undefined

    // Render status icon
    const renderStatusIcon = () => {
      if (loading) {
        return (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        )
      }

      switch (validationState) {
        case "error":
          return (
            <AlertCircle className={cn(sizeStyles.icon, "text-destructive")} />
          )
        case "warning":
          return <Info className={cn(sizeStyles.icon, "text-yellow-600")} />
        case "success":
          return (
            <CheckCircle className={cn(sizeStyles.icon, "text-green-600")} />
          )
        case "info":
          return <HelpCircle className={cn(sizeStyles.icon, "text-blue-600")} />
        default:
          return null
      }
    }

    // Render interactive icons
    const renderInteractiveIcons = () => {
      const icons = []

      // Clear button
      if (clearable && value && String(value).length > 0) {
        icons.push(
          <button
            key="clear"
            type="button"
            onClick={handleClear}
            className="text-gray-400 transition-colors hover:text-gray-600 focus:text-gray-600 focus:outline-none"
            aria-label="Clear input"
          >
            <X className={sizeStyles.icon} />
          </button>
        )
      }

      // Password toggle
      if (type === "password" && showPasswordToggle) {
        icons.push(
          <button
            key="password-toggle"
            type="button"
            onClick={togglePasswordVisibility}
            className="text-gray-400 transition-colors hover:text-gray-600 focus:text-gray-600 focus:outline-none"
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? (
              <EyeOff className={sizeStyles.icon} />
            ) : (
              <Eye className={sizeStyles.icon} />
            )}
          </button>
        )
      }

      return icons
    }

    // Render messages
    const renderMessages = () => {
      const messages = [
        ...errorMessages.map((msg) => ({ type: "error", message: msg })),
        ...warningMessages.map((msg) => ({ type: "warning", message: msg })),
        ...successMessages.map((msg) => ({ type: "success", message: msg })),
        ...infoMessages.map((msg) => ({ type: "info", message: msg })),
      ]

      if (messages.length === 0 && !helpText) return null

      return (
        <div className="space-y-1" id={messagesId}>
          {messages.map(({ type, message }, index) => (
            <p
              key={`${type}-${index}`}
              className={cn(
                sizeStyles.message,
                "flex items-center gap-1",
                type === "error" && "text-destructive",
                type === "warning" && "text-yellow-700",
                type === "success" && "text-green-700",
                type === "info" && "text-blue-700"
              )}
            >
              {type === "error" && (
                <AlertCircle className="h-3 w-3 flex-shrink-0" />
              )}
              {type === "warning" && <Info className="h-3 w-3 flex-shrink-0" />}
              {type === "success" && (
                <CheckCircle className="h-3 w-3 flex-shrink-0" />
              )}
              {type === "info" && (
                <HelpCircle className="h-3 w-3 flex-shrink-0" />
              )}
              {message}
            </p>
          ))}

          {helpText && !hasMessages && (
            <p
              id={helpTextId}
              className={cn(sizeStyles.message, "text-muted-foreground")}
            >
              {helpText}
            </p>
          )}
        </div>
      )
    }

    const interactiveIcons = renderInteractiveIcons()
    const statusIcon = renderStatusIcon()

    return (
      <div
        className={cn(
          formFieldVariants({ size, state: validationState }),
          "space-y-2"
        )}
      >
        {/* Label */}
        <div className="flex items-center justify-between">
          <Label
            htmlFor={id}
            className={cn(
              sizeStyles.label,
              "font-medium",
              validationState === "error" && "text-destructive",
              validationState === "warning" && "text-yellow-700",
              validationState === "success" && "text-green-700",
              validationState === "info" && "text-blue-700"
            )}
          >
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
            {optional && (
              <span className="text-muted-foreground ml-1">(optional)</span>
            )}
          </Label>

          {tooltip && (
            <div className="group relative">
              <HelpCircle className="text-muted-foreground h-4 w-4 cursor-help" />
              <div className="bg-popover text-popover-foreground invisible absolute top-6 right-0 z-10 w-64 rounded-md border p-2 text-sm shadow-md group-hover:visible">
                {tooltip}
              </div>
            </div>
          )}
        </div>

        {/* Description */}
        {description && (
          <p
            id={descriptionId}
            className={cn(sizeStyles.message, "text-muted-foreground")}
          >
            {description}
          </p>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Prefix */}
          {prefix && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              {prefix}
            </div>
          )}

          {/* Input Field */}
          {type === "textarea" ? (
            <Textarea
              ref={ref as React.ForwardedRef<HTMLTextAreaElement>}
              id={id}
              value={value}
              onChange={onChange}
              onFocus={onFocus}
              onBlur={onBlur}
              className={cn(
                sizeStyles.textarea,
                validationState === "error" &&
                  "border-destructive focus-visible:ring-destructive",
                validationState === "warning" &&
                  "border-yellow-400 focus-visible:ring-yellow-400",
                validationState === "success" &&
                  "border-green-400 focus-visible:ring-green-400",
                validationState === "info" &&
                  "border-blue-400 focus-visible:ring-blue-400",
                prefix && "pl-10",
                (suffix || statusIcon || interactiveIcons.length > 0) &&
                  "pr-10",
                className
              )}
              aria-describedby={ariaDescribedBy}
              aria-invalid={validationState === "error"}
              {...(props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
            />
          ) : type === "hidden" ? (
            <input
              ref={ref as React.ForwardedRef<HTMLInputElement>}
              type="hidden"
              id={id}
              value={value}
              onChange={onChange}
              {...(props as React.InputHTMLAttributes<HTMLInputElement>)}
            />
          ) : (
            <Input
              ref={ref as React.ForwardedRef<HTMLInputElement>}
              type={actualType}
              id={id}
              value={value}
              onChange={onChange}
              onFocus={onFocus}
              onBlur={onBlur}
              className={cn(
                sizeStyles.input,
                validationState === "error" &&
                  "border-destructive focus-visible:ring-destructive",
                validationState === "warning" &&
                  "border-yellow-400 focus-visible:ring-yellow-400",
                validationState === "success" &&
                  "border-green-400 focus-visible:ring-green-400",
                validationState === "info" &&
                  "border-blue-400 focus-visible:ring-blue-400",
                prefix && "pl-10",
                (suffix || statusIcon || interactiveIcons.length > 0) &&
                  "pr-10",
                type === "search" && "pr-8", // Space for search icon
                className
              )}
              aria-describedby={ariaDescribedBy}
              aria-invalid={validationState === "error"}
              {...(props as React.InputHTMLAttributes<HTMLInputElement>)}
            />
          )}

          {/* Search Icon */}
          {type === "search" && !prefix && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              <Search
                className={cn(sizeStyles.icon, "text-muted-foreground")}
              />
            </div>
          )}

          {/* Right side icons */}
          {type !== "textarea" &&
            type !== "hidden" &&
            (suffix || statusIcon || interactiveIcons.length > 0) && (
              <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                {suffix}
                {statusIcon}
                {interactiveIcons}
              </div>
            )}
        </div>

        {/* Messages */}
        {renderMessages()}
      </div>
    )
  }
)

FormField.displayName = "FormField"

// Convenience components for specific field types

export const EmailField = forwardRef<
  HTMLInputElement,
  Omit<FormFieldProps, "type">
>((props, ref) => <FormField ref={ref} type="email" {...props} />)
EmailField.displayName = "EmailField"

export const PasswordField = forwardRef<
  HTMLInputElement,
  Omit<FormFieldProps, "type">
>((props, ref) => (
  <FormField ref={ref} type="password" showPasswordToggle {...props} />
))
PasswordField.displayName = "PasswordField"

export const NumberField = forwardRef<
  HTMLInputElement,
  Omit<FormFieldProps, "type">
>((props, ref) => <FormField ref={ref} type="number" {...props} />)
NumberField.displayName = "NumberField"

export const SearchField = forwardRef<
  HTMLInputElement,
  Omit<FormFieldProps, "type">
>((props, ref) => <FormField ref={ref} type="search" clearable {...props} />)
SearchField.displayName = "SearchField"

export const TextAreaField = forwardRef<
  HTMLTextAreaElement,
  Omit<FormFieldProps, "type">
>((props, ref) => <FormField ref={ref} type="textarea" {...props} />)
TextAreaField.displayName = "TextAreaField"

export const TelephoneField = forwardRef<
  HTMLInputElement,
  Omit<FormFieldProps, "type">
>((props, ref) => <FormField ref={ref} type="tel" {...props} />)
TelephoneField.displayName = "TelephoneField"

export const UrlField = forwardRef<
  HTMLInputElement,
  Omit<FormFieldProps, "type">
>((props, ref) => <FormField ref={ref} type="url" {...props} />)
UrlField.displayName = "UrlField"

// Utility functions

export const validateField = (
  value: string | number | undefined,
  type: FieldType,
  required: boolean = false
): { isValid: boolean; error?: string } => {
  if (required && (!value || String(value).trim() === "")) {
    return { isValid: false, error: "This field is required" }
  }

  if (!value || String(value).trim() === "") {
    return { isValid: true }
  }

  const stringValue = String(value)

  switch (type) {
    case "email": {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return {
        isValid: emailRegex.test(stringValue),
        error: emailRegex.test(stringValue)
          ? undefined
          : "Please enter a valid email address",
      }
    }

    case "url":
      try {
        new URL(
          stringValue.startsWith("http")
            ? stringValue
            : `https://${stringValue}`
        )
        return { isValid: true }
      } catch {
        return { isValid: false, error: "Please enter a valid URL" }
      }

    case "tel": {
      const phoneRegex = /^[+]?[0-9\s-()]+$/
      return {
        isValid: phoneRegex.test(stringValue),
        error: phoneRegex.test(stringValue)
          ? undefined
          : "Please enter a valid phone number",
      }
    }

    case "number": {
      const isValidNumber = !isNaN(Number(stringValue))
      return {
        isValid: isValidNumber,
        error: isValidNumber ? undefined : "Please enter a valid number",
      }
    }

    default:
      return { isValid: true }
  }
}

export const getFieldTypeFromString = (type: string): FieldType => {
  const validTypes: FieldType[] = [
    "text",
    "email",
    "password",
    "number",
    "tel",
    "url",
    "search",
    "textarea",
    "hidden",
  ]
  return validTypes.includes(type as FieldType) ? (type as FieldType) : "text"
}

// Export types for external use
export type FormFieldSize = keyof typeof sizeConfig
export type FormFieldVariant = NonNullable<FormFieldProps["variant"]>
export type FormFieldType = FieldType

// Export configuration for external use
export { sizeConfig, formFieldVariants }
