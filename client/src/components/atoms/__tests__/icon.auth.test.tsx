/**
 * @file Auth-specific Icon component tests
 * @description TDD tests for Icon component auth features
 */

import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { UnifiedIcon } from '../icon'

describe('Icon - Auth Features', () => {
  describe('Auth-specific icons', () => {
    it('should render user icon', () => {
      render(<UnifiedIcon type="user" data-testid="user-icon" />)
      
      const icon = screen.getByTestId('user-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-label', 'user icon')
    })

    it('should render lock icon for security', () => {
      render(<UnifiedIcon type="lock" data-testid="lock-icon" />)
      
      const icon = screen.getByTestId('lock-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-label', 'lock icon')
    })

    it('should render unlock/key icon', () => {
      render(<UnifiedIcon type="unlock" data-testid="unlock-icon" />)
      
      const icon = screen.getByTestId('unlock-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-label', 'unlock icon')
    })

    it('should render mail icon for email fields', () => {
      render(<UnifiedIcon type="mail" data-testid="mail-icon" />)
      
      const icon = screen.getByTestId('mail-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-label', 'mail icon')
    })
  })

  describe('Password visibility icons', () => {
    it('should render eye icon for show password', () => {
      render(<UnifiedIcon type="eye" data-testid="show-password-icon" />)
      
      const icon = screen.getByTestId('show-password-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-label', 'eye icon')
    })

    // Note: Need to add EyeOff icon to the icon system
    it('should support custom icons for hide password', () => {
      const MockEyeOffIcon = ({ className }: { className?: string }) => (
        <div className={className} data-testid="eye-off-icon">EyeOff</div>
      )
      
      render(
        <UnifiedIcon
          type="eye-off"
          customIcon={MockEyeOffIcon}
          data-testid="hide-password-icon"
        />
      )
      
      const icon = screen.getByTestId('hide-password-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-label', 'eye-off icon')
    })
  })

  describe('Status and feedback icons', () => {
    it('should render success icon with appropriate color', () => {
      render(
        <UnifiedIcon
          type="success"
          color="success"
          data-testid="success-icon"
        />
      )
      
      const icon = screen.getByTestId('success-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveClass('text-green-600')
    })

    it('should render error icon with destructive color', () => {
      render(
        <UnifiedIcon
          type="error"
          color="destructive"
          data-testid="error-icon"
        />
      )
      
      const icon = screen.getByTestId('error-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveClass('text-destructive')
    })

    it('should render warning icon with warning color', () => {
      render(
        <UnifiedIcon
          type="warning"
          color="warning"
          data-testid="warning-icon"
        />
      )
      
      const icon = screen.getByTestId('warning-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveClass('text-yellow-600')
    })

    it('should render info icon with info color', () => {
      render(
        <UnifiedIcon
          type="info"
          color="info"
          data-testid="info-icon"
        />
      )
      
      const icon = screen.getByTestId('info-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveClass('text-blue-600')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(<UnifiedIcon type="user" data-testid="accessible-icon" />)
      
      const icon = screen.getByTestId('accessible-icon')
      expect(icon).toHaveAttribute('role', 'img')
      expect(icon).toHaveAttribute('aria-label')
    })

    it('should hide decorative content from screen readers', () => {
      render(<UnifiedIcon type="user" data-testid="decorative-icon" />)
      
      const icon = screen.getByTestId('decorative-icon')
      const svgElement = icon.querySelector('svg') || icon.querySelector('[aria-hidden="true"]')
      expect(svgElement).toHaveAttribute('aria-hidden', 'true')
    })
  })

  describe('Sizing and styling', () => {
    it('should support different sizes', () => {
      render(
        <>
          <UnifiedIcon type="user" size="sm" data-testid="small-icon" />
          <UnifiedIcon type="user" size="md" data-testid="medium-icon" />
          <UnifiedIcon type="user" size="lg" data-testid="large-icon" />
        </>
      )
      
      expect(screen.getByTestId('small-icon')).toHaveClass('w-4 h-4')
      expect(screen.getByTestId('medium-icon')).toHaveClass('w-5 h-5')
      expect(screen.getByTestId('large-icon')).toHaveClass('w-6 h-6')
    })

    it('should support custom className', () => {
      render(
        <UnifiedIcon
          type="user"
          className="custom-icon-class"
          data-testid="custom-icon"
        />
      )
      
      const icon = screen.getByTestId('custom-icon')
      expect(icon).toHaveClass('custom-icon-class')
    })
  })

  describe('Professional credentials icons', () => {
    it('should render shield icon for security/credentials', () => {
      render(<UnifiedIcon type="shield" data-testid="credentials-icon" />)
      
      const icon = screen.getByTestId('credentials-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-label', 'shield icon')
    })

    it('should render user-plus icon for registration', () => {
      render(<UnifiedIcon type="add_user" data-testid="register-icon" />)
      
      const icon = screen.getByTestId('register-icon')
      expect(icon).toBeInTheDocument()
      expect(icon).toHaveAttribute('aria-label', 'add_user icon')
    })
  })
})