/**
 * @file Auth-specific Label component tests
 * @description TDD tests for Label component auth features
 */

import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Label } from '../label'

describe('Label - Auth Features', () => {
  describe('Form field labels', () => {
    it('should render email field label correctly', () => {
      render(
        <Label htmlFor="email" data-testid="email-label">
          Email Address
        </Label>
      )

      const label = screen.getByTestId('email-label')
      expect(label).toBeInTheDocument()
      expect(label).toHaveAttribute('for', 'email')
      expect(label).toHaveTextContent('Email Address')
    })

    it('should render password field label correctly', () => {
      render(
        <Label htmlFor="password" data-testid="password-label">
          Password
        </Label>
      )

      const label = screen.getByTestId('password-label')
      expect(label).toBeInTheDocument()
      expect(label).toHaveAttribute('for', 'password')
      expect(label).toHaveTextContent('Password')
    })

    it('should render required field indicators', () => {
      render(
        <Label htmlFor="required-field" data-testid="required-label">
          Full Name <span className="text-destructive">*</span>
        </Label>
      )

      const label = screen.getByTestId('required-label')
      expect(label).toBeInTheDocument()
      expect(label).toHaveTextContent('Full Name *')
    })
  })

  describe('Professional credentials labels', () => {
    it('should render professional license label', () => {
      render(
        <Label htmlFor="professional-license" data-testid="license-label">
          Professional Engineering License (Optional)
        </Label>
      )

      const label = screen.getByTestId('license-label')
      expect(label).toBeInTheDocument()
      expect(label).toHaveAttribute('for', 'professional-license')
      expect(label).toHaveTextContent('Professional Engineering License (Optional)')
    })

    it('should render company name label', () => {
      render(
        <Label htmlFor="company-name" data-testid="company-label">
          Company Name
        </Label>
      )

      const label = screen.getByTestId('company-label')
      expect(label).toBeInTheDocument()
      expect(label).toHaveAttribute('for', 'company-name')
    })

    it('should render years of experience label', () => {
      render(
        <Label htmlFor="years-experience" data-testid="experience-label">
          Years of Experience
        </Label>
      )

      const label = screen.getByTestId('experience-label')
      expect(label).toBeInTheDocument()
      expect(label).toHaveAttribute('for', 'years-experience')
    })
  })

  describe('Accessibility and styling', () => {
    it('should apply correct styling classes', () => {
      render(
        <Label data-testid="styled-label">
          Styled Label
        </Label>
      )

      const label = screen.getByTestId('styled-label')
      expect(label).toHaveClass('text-foreground')
      expect(label).toHaveClass('text-sm')
      expect(label).toHaveClass('font-medium')
      expect(label).toHaveClass('select-none')
    })

    it('should support disabled state styling', () => {
      render(
        <div className="group" data-disabled="true">
          <Label data-testid="disabled-label">
            Disabled Label
          </Label>
        </div>
      )

      const label = screen.getByTestId('disabled-label')
      expect(label).toHaveClass('group-data-[disabled=true]:opacity-50')
    })

    it('should support peer-disabled styling', () => {
      render(
        <div>
          <input type="text" disabled className="peer" />
          <Label data-testid="peer-disabled-label">
            Peer Disabled Label
          </Label>
        </div>
      )

      const label = screen.getByTestId('peer-disabled-label')
      expect(label).toHaveClass('peer-disabled:opacity-50')
      expect(label).toHaveClass('peer-disabled:cursor-not-allowed')
    })

    it('should support custom className', () => {
      render(
        <Label className="custom-label-class" data-testid="custom-label">
          Custom Label
        </Label>
      )

      const label = screen.getByTestId('custom-label')
      expect(label).toHaveClass('custom-label-class')
    })
  })

  describe('Form association', () => {
    it('should properly associate with form controls', () => {
      render(
        <div>
          <Label htmlFor="associated-input" data-testid="form-label">
            Associated Label
          </Label>
          <input id="associated-input" type="text" />
        </div>
      )

      const label = screen.getByTestId('form-label')
      const input = screen.getByRole('textbox')
      
      expect(label).toHaveAttribute('for', 'associated-input')
      expect(input).toHaveAttribute('id', 'associated-input')
    })

    it('should support aria-labelledby relationships', () => {
      render(
        <div>
          <Label id="label-for-input" data-testid="aria-label">
            ARIA Label
          </Label>
          <input aria-labelledby="label-for-input" type="text" />
        </div>
      )

      const label = screen.getByTestId('aria-label')
      const input = screen.getByRole('textbox')
      
      expect(label).toHaveAttribute('id', 'label-for-input')
      expect(input).toHaveAttribute('aria-labelledby', 'label-for-input')
    })
  })

  describe('Help text and error labels', () => {
    it('should render help text with appropriate styling', () => {
      render(
        <Label className="text-muted-foreground text-xs" data-testid="help-text">
          Enter your email address for password reset
        </Label>
      )

      const helpText = screen.getByTestId('help-text')
      expect(helpText).toHaveClass('text-muted-foreground')
      expect(helpText).toHaveClass('text-xs')
    })

    it('should render error labels with destructive styling', () => {
      render(
        <Label className="text-destructive text-xs" data-testid="error-label">
          This field is required
        </Label>
      )

      const errorLabel = screen.getByTestId('error-label')
      expect(errorLabel).toHaveClass('text-destructive')
      expect(errorLabel).toHaveClass('text-xs')
    })
  })
})