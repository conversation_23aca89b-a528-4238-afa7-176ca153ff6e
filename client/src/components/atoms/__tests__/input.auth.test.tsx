/**
 * @file Auth-specific Input component tests
 * @description TDD tests for Input component auth features
 */

import { describe, it, expect } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { Input } from '../input'

describe('Input - Auth Features', () => {
  describe('Error states', () => {
    it('should apply error styling when aria-invalid is true', () => {
      render(
        <Input
          data-testid="error-input"
          aria-invalid="true"
          placeholder="Enter email"
        />
      )

      const input = screen.getByTestId('error-input')
      expect(input).toHaveAttribute('aria-invalid', 'true')
      expect(input).toHaveClass('aria-invalid:border-destructive')
    })

    it('should not apply error styling when aria-invalid is false', () => {
      render(
        <Input
          data-testid="normal-input"
          aria-invalid="false"
          placeholder="Enter email"
        />
      )

      const input = screen.getByTestId('normal-input')
      expect(input).toHaveAttribute('aria-invalid', 'false')
      expect(input).toHaveClass('aria-invalid:border-destructive')
    })
  })

  describe('Password input', () => {
    it('should render password input correctly', () => {
      render(
        <Input
          type="password"
          data-testid="password-input"
          placeholder="Enter password"
        />
      )

      const input = screen.getByTestId('password-input')
      expect(input).toHaveAttribute('type', 'password')
    })

    it('should handle password input changes', () => {
      render(
        <Input
          type="password"
          data-testid="password-input"
          placeholder="Enter password"
        />
      )

      const input = screen.getByTestId('password-input')
      fireEvent.change(input, { target: { value: 'secret123' } })
      
      expect(input).toHaveValue('secret123')
    })
  })

  describe('Email input', () => {
    it('should render email input correctly', () => {
      render(
        <Input
          type="email"
          data-testid="email-input"
          placeholder="Enter email"
        />
      )

      const input = screen.getByTestId('email-input')
      expect(input).toHaveAttribute('type', 'email')
    })

    it('should handle email input changes', () => {
      render(
        <Input
          type="email"
          data-testid="email-input"
          placeholder="Enter email"
        />
      )

      const input = screen.getByTestId('email-input')
      fireEvent.change(input, { target: { value: '<EMAIL>' } })
      
      expect(input).toHaveValue('<EMAIL>')
    })
  })

  describe('Accessibility', () => {
    it('should support aria-describedby for error messages', () => {
      render(
        <Input
          data-testid="accessible-input"
          aria-describedby="email-error"
          aria-invalid="true"
        />
      )

      const input = screen.getByTestId('accessible-input')
      expect(input).toHaveAttribute('aria-describedby', 'email-error')
    })

    it('should support required attribute', () => {
      render(
        <Input
          data-testid="required-input"
          required
          placeholder="Required field"
        />
      )

      const input = screen.getByTestId('required-input')
      expect(input).toHaveAttribute('required')
    })

    it('should support autoComplete attribute for auth fields', () => {
      render(
        <>
          <Input
            type="email"
            data-testid="email-input"
            autoComplete="email"
          />
          <Input
            type="password"
            data-testid="password-input"
            autoComplete="current-password"
          />
          <Input
            type="password"
            data-testid="new-password-input"
            autoComplete="new-password"
          />
        </>
      )

      expect(screen.getByTestId('email-input')).toHaveAttribute('autoComplete', 'email')
      expect(screen.getByTestId('password-input')).toHaveAttribute('autoComplete', 'current-password')
      expect(screen.getByTestId('new-password-input')).toHaveAttribute('autoComplete', 'new-password')
    })
  })

  describe('Focus and styling', () => {
    it('should apply focus-visible styles', () => {
      render(<Input data-testid="focus-input" />)
      
      const input = screen.getByTestId('focus-input')
      expect(input).toHaveClass('focus-visible:border-ring')
      expect(input).toHaveClass('focus-visible:ring-ring/50')
    })

    it('should handle disabled state', () => {
      render(<Input data-testid="disabled-input" disabled />)
      
      const input = screen.getByTestId('disabled-input')
      expect(input).toBeDisabled()
      expect(input).toHaveClass('disabled:opacity-50')
    })
  })
})