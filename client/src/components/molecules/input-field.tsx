/**
 * @file InputField molecule component
 * @description Combines Label, Input, and validation messaging into a cohesive form field
 */

import * as React from 'react'
import { cn } from '@/lib/utils'
import { Label } from '@/components/atoms/label'
import { Input } from '@/components/atoms/input'

export interface InputFieldProps 
  extends Omit<React.ComponentProps<typeof Input>, 'aria-describedby' | 'aria-invalid' | 'data-testid'> {
  label?: string
  error?: string | string[]
  helpText?: string
  required?: boolean
  className?: string
  'data-testid'?: string
}

export const InputField = React.forwardRef<HTMLInputElement, InputFieldProps>(
  ({ 
    label, 
    error, 
    helpText, 
    required = false, 
    className, 
    id,
    'data-testid': dataTestId,
    ...inputProps 
  }, ref) => {
    // Generate unique IDs for accessibility
    const fieldId = id || React.useId()
    const errorId = error ? `${fieldId}-error` : undefined
    const helpTextId = helpText ? `${fieldId}-help` : undefined
    
    // Combine description IDs for aria-describedby
    const describedByIds = [helpTextId, errorId].filter(Boolean).join(' ')
    
    // Determine if field has error
    const hasError = Boolean(error)
    
    // Handle error messages (string or array)
    const errorMessages = React.useMemo(() => {
      if (!error) return []
      return Array.isArray(error) ? error : [error]
    }, [error])

    return (
      <div className={cn('space-y-2', className)} data-testid={dataTestId}>
        {label && (
          <Label htmlFor={fieldId} className="block">
            {label}
            {required && (
              <span className="text-destructive ml-1" aria-label="required">
                *
              </span>
            )}
          </Label>
        )}
        
        <Input
          ref={ref}
          id={fieldId}
          required={required}
          aria-invalid={hasError}
          aria-describedby={describedByIds || undefined}
          className={cn(
            hasError && 'border-destructive focus-visible:border-destructive'
          )}
          {...inputProps}
        />

        {helpText && (
          <p 
            id={helpTextId}
            className="text-xs text-muted-foreground"
          >
            {helpText}
          </p>
        )}

        {hasError && (
          <div id={errorId} role="alert" className="space-y-1">
            {errorMessages.map((message, index) => (
              <p key={index} className="text-xs text-destructive">
                {message}
              </p>
            ))}
          </div>
        )}
      </div>
    )
  }
)

InputField.displayName = 'InputField'