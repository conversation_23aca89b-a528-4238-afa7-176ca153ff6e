/**
 * @file InputField molecule tests
 * @description TDD tests for InputField molecule component
 */

import { describe, it, expect } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { InputField } from '../input-field'

describe('InputField Molecule', () => {
  describe('Basic rendering', () => {
    it('should render label, input, and combine them correctly', () => {
      render(
        <InputField
          label="Email Address"
          id="test-email"
          type="email"
          placeholder="Enter your email"
          data-testid="email-field"
        />
      )

      const input = screen.getByLabelText('Email Address')
      const label = screen.getByText('Email Address')
      
      expect(input).toBeInTheDocument()
      expect(label).toBeInTheDocument()
      expect(input).toHaveAttribute('type', 'email')
      expect(input).toHaveAttribute('placeholder', 'Enter your email')
      expect(input).toHaveAttribute('id', 'test-email')
      expect(label).toHaveAttribute('for', 'test-email')
    })

    it('should render without label when not provided', () => {
      render(
        <InputField
          id="no-label-input"
          placeholder="No label input"
          data-testid="no-label-field"
        />
      )

      const input = screen.getByPlaceholderText('No label input')
      expect(input).toBeInTheDocument()
      
      // Should not find a label
      expect(screen.queryByRole('label')).not.toBeInTheDocument()
    })
  })

  describe('Required field handling', () => {
    it('should show required indicator when required', () => {
      render(
        <InputField
          label="Required Field"
          id="required-input"
          required
          data-testid="required-field"
        />
      )

      const label = screen.getByText(/Required Field/)
      const input = screen.getByLabelText(/Required Field/)
      
      expect(label).toBeInTheDocument()
      expect(input).toHaveAttribute('required')
      // Look for required indicator (*)
      expect(screen.getByText('*')).toBeInTheDocument()
    })

    it('should not show required indicator when optional', () => {
      render(
        <InputField
          label="Optional Field"
          id="optional-input"
          data-testid="optional-field"
        />
      )

      expect(screen.queryByText('*')).not.toBeInTheDocument()
    })
  })

  describe('Error state handling', () => {
    it('should display error message when provided', () => {
      render(
        <InputField
          label="Email"
          id="error-input"
          error="Please enter a valid email address"
          data-testid="error-field"
        />
      )

      const input = screen.getByLabelText('Email')
      const errorMessage = screen.getByText('Please enter a valid email address')
      
      expect(errorMessage).toBeInTheDocument()
      expect(input).toHaveAttribute('aria-invalid', 'true')
      expect(input).toHaveAttribute('aria-describedby')
    })

    it('should not show error message when no error', () => {
      render(
        <InputField
          label="Email"
          id="no-error-input"
          data-testid="no-error-field"
        />
      )

      expect(screen.queryByRole('alert')).not.toBeInTheDocument()
    })

    it('should handle array of error messages', () => {
      render(
        <InputField
          label="Password"
          id="multi-error-input"
          error={['Password too short', 'Missing special characters']}
          data-testid="multi-error-field"
        />
      )

      expect(screen.getByText('Password too short')).toBeInTheDocument()
      expect(screen.getByText('Missing special characters')).toBeInTheDocument()
    })
  })

  describe('Help text', () => {
    it('should display help text when provided', () => {
      render(
        <InputField
          label="Username"
          id="help-input"
          helpText="Username must be 3-20 characters long"
          data-testid="help-field"
        />
      )

      const helpText = screen.getByText('Username must be 3-20 characters long')
      const input = screen.getByLabelText('Username')
      
      expect(helpText).toBeInTheDocument()
      expect(input).toHaveAttribute('aria-describedby')
    })

    it('should associate help text with input for accessibility', () => {
      render(
        <InputField
          label="Username"
          id="accessible-help-input"
          helpText="Help text for accessibility"
          data-testid="accessible-help-field"
        />
      )

      const input = screen.getByLabelText('Username')
      const helpTextId = input.getAttribute('aria-describedby')
      
      expect(helpTextId).toBeTruthy()
      expect(screen.getByText('Help text for accessibility')).toHaveAttribute('id', helpTextId)
    })
  })

  describe('Interaction handling', () => {
    it('should handle value changes', () => {
      render(
        <InputField
          label="Test Input"
          id="change-input"
          data-testid="change-field"
        />
      )

      const input = screen.getByLabelText('Test Input') as HTMLInputElement
      fireEvent.change(input, { target: { value: 'test value' } })
      
      expect(input.value).toBe('test value')
    })

    it('should handle focus and blur events', () => {
      render(
        <InputField
          label="Focus Input"
          id="focus-input"
          data-testid="focus-field"
        />
      )

      const input = screen.getByLabelText('Focus Input')
      
      // Use userEvent or direct DOM manipulation for better focus testing
      input.focus()
      expect(document.activeElement).toBe(input)
      
      input.blur()
      expect(document.activeElement).not.toBe(input)
    })
  })

  describe('Styling and customization', () => {
    it('should support custom className', () => {
      render(
        <InputField
          label="Custom Field"
          id="custom-input"
          className="custom-input-field"
          data-testid="custom-field"
        />
      )

      const container = screen.getByTestId('custom-field')
      expect(container).toHaveClass('custom-input-field')
    })

    it('should apply error styling when error is present', () => {
      render(
        <InputField
          label="Error Field"
          id="styled-error-input"
          error="Error message"
          data-testid="styled-error-field"
        />
      )

      const input = screen.getByLabelText('Error Field')
      expect(input).toHaveAttribute('aria-invalid', 'true')
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(
        <InputField
          label="Accessible Field"
          id="aria-input"
          helpText="Help text"
          error="Error message"
          required
          data-testid="aria-field"
        />
      )

      const input = screen.getByLabelText(/Accessible Field/)
      
      expect(input).toHaveAttribute('aria-required', 'true')
      expect(input).toHaveAttribute('aria-invalid', 'true')
      expect(input).toHaveAttribute('aria-describedby')
    })

    it('should support additional ARIA attributes', () => {
      render(
        <InputField
          label="ARIA Field"
          id="additional-aria-input"
          aria-autocomplete="list"
          aria-expanded="false"
          data-testid="additional-aria-field"
        />
      )

      const input = screen.getByLabelText('ARIA Field')
      expect(input).toHaveAttribute('aria-autocomplete', 'list')
      expect(input).toHaveAttribute('aria-expanded', 'false')
    })
  })

  describe('Professional form fields', () => {
    it('should handle professional license input', () => {
      render(
        <InputField
          label="Professional Engineering License"
          id="pe-license"
          placeholder="e.g., PE123456"
          helpText="Enter your professional engineering license number (optional)"
          data-testid="pe-license-field"
        />
      )

      const input = screen.getByLabelText('Professional Engineering License')
      const helpText = screen.getByText('Enter your professional engineering license number (optional)')
      
      expect(input).toHaveAttribute('placeholder', 'e.g., PE123456')
      expect(helpText).toBeInTheDocument()
    })

    it('should handle company name input', () => {
      render(
        <InputField
          label="Company Name"
          id="company"
          placeholder="Your engineering firm"
          required
          data-testid="company-field"
        />
      )

      const input = screen.getByLabelText(/Company Name/)
      expect(input).toHaveAttribute('required')
      expect(input).toHaveAttribute('placeholder', 'Your engineering firm')
      expect(screen.getByText('*')).toBeInTheDocument()
    })

    it('should handle years of experience input', () => {
      render(
        <InputField
          label="Years of Experience"
          id="experience"
          type="number"
          min="0"
          max="50"
          placeholder="0"
          data-testid="experience-field"
        />
      )

      const input = screen.getByLabelText('Years of Experience')
      expect(input).toHaveAttribute('type', 'number')
      expect(input).toHaveAttribute('min', '0')
      expect(input).toHaveAttribute('max', '50')
    })
  })
})