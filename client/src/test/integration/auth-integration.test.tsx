/**
 * Authentication Integration Tests
 * Tests the complete authentication flow including token management,
 * route protection, and session persistence
 */

import { useAuthStore } from "@/stores/authStore"
import {
  act,
  renderWithProviders,
  screen,
  userEvent,
  waitFor,
} from "@/test/utils"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { apiClient } from "@/lib/api/client"
import { TokenManager } from "@/lib/auth/tokenManager"
import { useAuth } from "@/hooks/useAuth"

import { RouteGuard } from "@/components/modules/auth/RouteGuard"

import { userRoleSchema } from "../../../../.old/types/schemas/enums/projectManagementEnums"

// Mock Next.js router
const mockPush = vi.fn()
vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: mockPush,
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  }),
  usePathname: () => "/dashboard",
  useSearchParams: () => new URLSearchParams(),
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
})

// Mock API client
vi.mock("@/lib/api/client", () => ({
  apiClient: {
    login: vi.fn(),
    logout: vi.fn(),
    getCurrentUser: vi.fn(),
    setAuthToken: vi.fn(),
    clearAuthToken: vi.fn(),
  },
}))

// Mock TokenManager
vi.mock("@/lib/auth/tokenManager", () => ({
  TokenManager: {
    decodeToken: vi.fn(),
    initializeTokens: vi.fn(),
    setAccessToken: vi.fn(),
    getAccessToken: vi.fn(),
    clearTokens: vi.fn(),
    isTokenExpired: vi.fn(),
  },
}))

// Mock useAuth hook
vi.mock("@/hooks/useAuth", () => ({
  useAuth: vi.fn(),
}))

const mockApiClient = vi.mocked(apiClient)

describe("Authentication Integration", () => {
  const mockUser = {
    id: 1,
    name: "Test User",
    email: "<EMAIL>",
    role: userRoleSchema.enum.Viewer,
    is_active: true,
    is_superuser: false,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    last_login: new Date("2024-01-01T00:00:00Z"),
  }

  const mockLoginResponse = {
    access_token: "mock-jwt-token",
    token_type: "bearer",
    expires_in: 3600,
    user: mockUser,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
    useAuthStore.getState().clearAuth()

    // Reset useAuth mock to default implementation
    vi.mocked(useAuth).mockImplementation(() => {
      const store = useAuthStore.getState()
      return {
        isAuthenticated: !!store.user,
        isLoading: false,
        user: store.user,
        isAdmin: () => store.user?.is_superuser || false,
        hasRole: (role: string) => store.user?.role === role,
        login: vi.fn(),
        logout: vi.fn(),
        loginError: null,
      } as any
    })
  })

  describe("Complete Authentication Flow", () => {
    it("should handle complete login flow with token management", async () => {
      const user = userEvent.setup()
      mockApiClient.login.mockResolvedValue(mockLoginResponse)

      // Mock useAuth to simulate login behavior with proper token management
      let isAuthenticatedState = false
      let userState: any = null

      vi.mocked(useAuth).mockImplementation(
        () =>
          ({
            login: async (credentials: any) => {
              const response = await mockApiClient.login(credentials)

              // Simulate the real token management flow
              mockLocalStorage.setItem("access_token", response.access_token)
              mockApiClient.setAuthToken(response.access_token)

              isAuthenticatedState = true
              userState = response.user
              useAuthStore
                .getState()
                .setAuth(response.user, response.access_token)
              return response
            },
            isAuthenticated: isAuthenticatedState,
            user: userState,
            token: isAuthenticatedState ? "mock-jwt-token" : null,
            isLoading: false,
            isAdmin: () => false,
            hasRole: () => true,
            logout: vi.fn(),
            loginError: null,
            requireAuth: vi.fn(),
            requireAdmin: vi.fn(),
            logoutError: null,
            isLoginPending: false,
            isLogoutPending: false,
          }) as any
      )

      const TestComponent = () => {
        const { login, isAuthenticated, user: authUser } = useAuth()

        const handleLogin = async () => {
          await login({
            username: "<EMAIL>",
            password: "password123",
          })
        }

        return (
          <div>
            <button onClick={handleLogin}>Login</button>
            <div data-testid="auth-status">
              {isAuthenticated ? "Authenticated" : "Not Authenticated"}
            </div>
            <div data-testid="user-name">{authUser?.name || "No User"}</div>
          </div>
        )
      }

      const { rerender } = renderWithProviders(<TestComponent />)

      // Initial state
      expect(screen.getByTestId("auth-status")).toHaveTextContent(
        "Not Authenticated"
      )
      expect(screen.getByTestId("user-name")).toHaveTextContent("No User")

      // Perform login
      await act(async () => {
        await user.click(screen.getByText("Login"))
      })

      // Update mock to reflect authenticated state
      vi.mocked(useAuth).mockImplementation(
        () =>
          ({
            login: vi.fn(),
            isAuthenticated: true,
            user: mockUser,
            token: "mock-jwt-token",
            isLoading: false,
            isAdmin: () => false,
            hasRole: () => true,
            logout: vi.fn(),
            loginError: null,
            requireAuth: vi.fn(),
            requireAdmin: vi.fn(),
            logoutError: null,
            isLoginPending: false,
            isLogoutPending: false,
          }) as any
      )

      // Force re-render with new state
      rerender(<TestComponent />)

      // Wait for authentication to complete
      await waitFor(() => {
        expect(screen.getByTestId("auth-status")).toHaveTextContent(
          "Authenticated"
        )
      })

      expect(screen.getByTestId("user-name")).toHaveTextContent("Test User")

      // Verify API calls
      expect(mockApiClient.login).toHaveBeenCalledWith({
        username: "<EMAIL>",
        password: "password123",
      })

      // Verify token management
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        "access_token",
        "mock-jwt-token"
      )
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith("mock-jwt-token")
    })

    it("should handle logout flow with token cleanup", async () => {
      const user = userEvent.setup()
      mockApiClient.logout.mockResolvedValue({
        message: "Logged out successfully",
        logged_out_at: "2024-01-01T00:00:00Z",
      })

      // Set initial authenticated state
      act(() => {
        useAuthStore.getState().setAuth(mockUser, "mock-jwt-token")
      })

      // Mock useAuth to simulate logout behavior with proper token cleanup
      let isAuthenticatedState = true

      vi.mocked(useAuth).mockImplementation(
        () =>
          ({
            login: vi.fn(),
            logout: async () => {
              await mockApiClient.logout()

              // Simulate the real token cleanup flow
              mockLocalStorage.removeItem("access_token")
              mockLocalStorage.removeItem("refresh_token")
              mockApiClient.clearAuthToken()

              isAuthenticatedState = false
              useAuthStore.getState().clearAuth()
            },
            isAuthenticated: isAuthenticatedState,
            user: mockUser,
            token: "mock-jwt-token",
            isLoading: false,
            isAdmin: () => false,
            hasRole: () => true,
            loginError: null,
            requireAuth: vi.fn(),
            requireAdmin: vi.fn(),
            logoutError: null,
            isLoginPending: false,
            isLogoutPending: false,
          }) as any
      )

      const TestComponent = () => {
        const { logout, isAuthenticated } = useAuth()

        const handleLogout = async () => {
          await logout()
        }

        return (
          <div>
            <button onClick={handleLogout}>Logout</button>
            <div data-testid="auth-status">
              {isAuthenticated ? "Authenticated" : "Not Authenticated"}
            </div>
          </div>
        )
      }

      const { rerender } = renderWithProviders(<TestComponent />)

      // Initial authenticated state
      expect(screen.getByTestId("auth-status")).toHaveTextContent(
        "Authenticated"
      )

      // Perform logout
      await act(async () => {
        await user.click(screen.getByText("Logout"))
      })

      // Update mock to reflect logged out state
      vi.mocked(useAuth).mockImplementation(
        () =>
          ({
            login: vi.fn(),
            logout: vi.fn(),
            isAuthenticated: false,
            user: null,
            token: null,
            isLoading: false,
            isAdmin: () => false,
            hasRole: () => true,
            loginError: null,
            requireAuth: vi.fn(),
            requireAdmin: vi.fn(),
            logoutError: null,
            isLoginPending: false,
            isLogoutPending: false,
          }) as any
      )

      // Force re-render with new state
      rerender(<TestComponent />)

      // Wait for logout to complete
      await waitFor(() => {
        expect(screen.getByTestId("auth-status")).toHaveTextContent(
          "Not Authenticated"
        )
      })

      // Verify API calls
      expect(mockApiClient.logout).toHaveBeenCalled()

      // Verify token cleanup
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith("access_token")
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith("refresh_token")
      expect(mockApiClient.clearAuthToken).toHaveBeenCalled()
    })
  })

  describe("Route Protection", () => {
    it("should redirect unauthenticated users from protected routes", async () => {
      const ProtectedComponent = () => <div>Protected Content</div>

      renderWithProviders(
        <RouteGuard requireAuth={true}>
          <ProtectedComponent />
        </RouteGuard>
      )

      // Should redirect to login
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/login")
      })
    })

    it("should allow authenticated users to access protected routes", async () => {
      // Set authenticated state
      act(() => {
        useAuthStore.getState().setAuth(mockUser, "mock-jwt-token")
      })

      const ProtectedComponent = () => <div>Protected Content</div>

      // Mock the useAuth hook to return authenticated state
      vi.mocked(useAuth).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        user: mockUser,
        isAdmin: () => false,
        hasRole: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        loginError: null,
      } as any)

      renderWithProviders(
        <RouteGuard requireAuth={true}>
          <ProtectedComponent />
        </RouteGuard>
      )

      // Wait for loading to complete and content to render
      await waitFor(() => {
        expect(screen.getByText("Protected Content")).toBeInTheDocument()
      })
      expect(mockPush).not.toHaveBeenCalled()
    })

    it("should redirect non-admin users from admin routes", async () => {
      // Mock the useAuth hook to return authenticated but non-admin state
      vi.mocked(useAuth).mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
        user: mockUser,
        isAdmin: () => false,
        hasRole: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        loginError: null,
      } as any)

      const AdminComponent = () => <div>Admin Content</div>

      renderWithProviders(
        <RouteGuard requireAuth={true} requireAdmin={true}>
          <AdminComponent />
        </RouteGuard>
      )

      // Should redirect to dashboard
      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith("/dashboard")
      })
    })
  })

  describe("Session Persistence", () => {
    it("should restore authentication state from localStorage on app start", () => {
      // Mock stored token
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === "access_token") return "stored-jwt-token"
        return null
      })

      // Create a spy for the initializeTokens method that actually calls the mocked methods
      const initSpy = vi
        .spyOn(TokenManager, "initializeTokens")
        .mockImplementation(() => {
          const token = TokenManager.getAccessToken()
          if (token && !TokenManager.isTokenExpired(token)) {
            // Token is valid, set it in API client
            mockApiClient.setAuthToken(token)
          }
        })

      // Mock getAccessToken to return the stored token
      vi.mocked(TokenManager.getAccessToken).mockReturnValue("stored-jwt-token")

      // Mock token expiration check
      vi.mocked(TokenManager.isTokenExpired).mockReturnValue(false)

      // Initialize tokens
      act(() => {
        TokenManager.initializeTokens()
      })

      // Verify the token manager methods were called
      expect(TokenManager.getAccessToken).toHaveBeenCalled()
      expect(TokenManager.isTokenExpired).toHaveBeenCalledWith(
        "stored-jwt-token"
      )
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith(
        "stored-jwt-token"
      )

      initSpy.mockRestore()
    })

    it("should clear expired tokens on app start", () => {
      // Mock stored expired token
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === "access_token") return "expired-jwt-token"
        return null
      })

      // Create a spy for the initializeTokens method that actually calls the mocked methods
      const initSpy = vi
        .spyOn(TokenManager, "initializeTokens")
        .mockImplementation(() => {
          const token = TokenManager.getAccessToken()
          if (token && TokenManager.isTokenExpired(token)) {
            // Token is expired, clear it
            TokenManager.clearTokens()
            mockApiClient.clearAuthToken()
          }
        })

      // Mock getAccessToken to return the expired token
      vi.mocked(TokenManager.getAccessToken).mockReturnValue(
        "expired-jwt-token"
      )

      // Mock token expiration check
      vi.mocked(TokenManager.isTokenExpired).mockReturnValue(true)

      // Initialize tokens
      act(() => {
        TokenManager.initializeTokens()
      })

      // Verify the token manager methods were called
      expect(TokenManager.getAccessToken).toHaveBeenCalled()
      expect(TokenManager.isTokenExpired).toHaveBeenCalledWith(
        "expired-jwt-token"
      )
      expect(TokenManager.clearTokens).toHaveBeenCalled()
      expect(mockApiClient.clearAuthToken).toHaveBeenCalled()

      initSpy.mockRestore()
    })
  })

  describe("Error Handling", () => {
    it("should handle login errors gracefully", async () => {
      const user = userEvent.setup()
      const loginError = new Error("Invalid credentials")
      mockApiClient.login.mockRejectedValue(loginError)

      // Mock useAuth to simulate error handling behavior
      let loginErrorState: any = null

      vi.mocked(useAuth).mockImplementation(
        () =>
          ({
            login: async (credentials: any) => {
              try {
                await mockApiClient.login(credentials)
              } catch (error) {
                loginErrorState = error
                throw error
              }
              return {} as any
            },
            isAuthenticated: false,
            user: null,
            token: null,
            isLoading: false,
            isAdmin: () => false,
            hasRole: () => true,
            logout: vi.fn(),
            loginError: loginErrorState,
            requireAuth: vi.fn(),
            requireAdmin: vi.fn(),
            logoutError: null,
            isLoginPending: false,
            isLogoutPending: false,
          }) as any
      )

      const TestComponent = () => {
        const { login, loginError: authError } = useAuth()

        const handleLogin = async () => {
          try {
            await login({
              username: "<EMAIL>",
              password: "wrongpassword",
            })
          } catch (error) {
            // Error is handled by useAuth hook
          }
        }

        return (
          <div>
            <button onClick={handleLogin}>Login</button>
            <div data-testid="error">{authError?.message || "No Error"}</div>
          </div>
        )
      }

      const { rerender } = renderWithProviders(<TestComponent />)

      // Perform failed login
      await act(async () => {
        await user.click(screen.getByText("Login"))
      })

      // Update mock to reflect error state
      vi.mocked(useAuth).mockImplementation(
        () =>
          ({
            login: vi.fn(),
            isAuthenticated: false,
            user: null,
            token: null,
            isLoading: false,
            isAdmin: () => false,
            hasRole: () => true,
            logout: vi.fn(),
            loginError: loginError,
            requireAuth: vi.fn(),
            requireAdmin: vi.fn(),
            logoutError: null,
            isLoginPending: false,
            isLogoutPending: false,
          }) as any
      )

      // Force re-render with error state
      rerender(<TestComponent />)

      // Should display error
      await waitFor(() => {
        expect(screen.getByTestId("error")).toHaveTextContent(
          "Invalid credentials"
        )
      })

      // Should not set tokens
      expect(mockLocalStorage.setItem).not.toHaveBeenCalled()
      expect(mockApiClient.setAuthToken).not.toHaveBeenCalled()
    })
  })
})
