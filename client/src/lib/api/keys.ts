/**
 * React Query keys for consistent caching and invalidation
 */

export const QueryKeys = {
  // Authentication
  auth: {
    me: ['auth', 'me'] as const,
    sessions: ['auth', 'sessions'] as const,
    activities: ['auth', 'activities'] as const,
  },
  
  // Users
  users: {
    all: ['users'] as const,
    list: (params?: Record<string, unknown>) => ['users', 'list', params] as const,
    detail: (id: number) => ['users', 'detail', id] as const,
    preferences: (userId: number) => ['users', 'preferences', userId] as const,
    activities: (userId: number) => ['users', 'activities', userId] as const,
  },

  // Components (for future use)
  components: {
    all: ['components'] as const,
    list: (params?: Record<string, unknown>) => ['components', 'list', params] as const,
    detail: (id: number) => ['components', 'detail', id] as const,
    categories: ['components', 'categories'] as const,
    types: ['components', 'types'] as const,
  },

  // Projects (for future use)
  projects: {
    all: ['projects'] as const,
    list: (params?: Record<string, unknown>) => ['projects', 'list', params] as const,
    detail: (id: number) => ['projects', 'detail', id] as const,
    members: (projectId: number) => ['projects', 'members', projectId] as const,
  },
} as const

export const MutationKeys = {
  // Authentication
  auth: {
    login: ['auth', 'login'] as const,
    logout: ['auth', 'logout'] as const,
    register: ['auth', 'register'] as const,
    changePassword: ['auth', 'changePassword'] as const,
    passwordReset: ['auth', 'passwordReset'] as const,
    confirmPasswordReset: ['auth', 'confirmPasswordReset'] as const,
    updateProfile: ['auth', 'updateProfile'] as const,
  },

  // Users
  users: {
    create: ['users', 'create'] as const,
    update: (id: number) => ['users', 'update', id] as const,
    delete: (id: number) => ['users', 'delete', id] as const,
    updatePreferences: (userId: number) => ['users', 'updatePreferences', userId] as const,
  },

  // Components (for future use)
  components: {
    create: ['components', 'create'] as const,
    update: (id: number) => ['components', 'update', id] as const,
    delete: (id: number) => ['components', 'delete', id] as const,
  },

  // Projects (for future use)  
  projects: {
    create: ['projects', 'create'] as const,
    update: (id: number) => ['projects', 'update', id] as const,
    delete: (id: number) => ['projects', 'delete', id] as const,
    addMember: (projectId: number) => ['projects', 'addMember', projectId] as const,
    removeMember: (projectId: number) => ['projects', 'removeMember', projectId] as const,
  },
} as const