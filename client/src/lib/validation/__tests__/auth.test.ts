/**
 * @file Auth validation test
 * @description Tests for authentication validation utilities
 */

import { describe, it, expect } from 'vitest'
import {
  validateEmail,
  validatePassword,
  validatePasswordsMatch,
  validateProfessionalLicense,
  validateYearsExperience,
  validateLoginForm,
  validateRegistrationForm,
  validatePasswordChangeForm,
  hasValidationErrors,
  getFirstErrorMessage,
  getPasswordStrength,
  COMMON_PASSWORDS,
  EMAIL_REGEX,
} from '../auth'
import type { LoginFormData, RegisterFormData, PasswordChangeRequest } from '@/types/auth'

describe('Auth Validation', () => {
  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBeNull()
      expect(validateEmail('<EMAIL>')).toBeNull()
      expect(validateEmail('<EMAIL>')).toBeNull()
    })

    it('should reject invalid email addresses', () => {
      expect(validateEmail('')).toBe('Email is required')
      expect(validateEmail('invalid-email')).toBe('Please enter a valid email address')
      expect(validateEmail('test@')).toBe('Please enter a valid email address')
      expect(validateEmail('@example.com')).toBe('Please enter a valid email address')
      // Note: <EMAIL> is technically valid by RFC standards
      expect(validateEmail('test@')).toBe('Please enter a valid email address')
    })
  })

  describe('validatePassword', () => {
    it('should validate strong passwords', () => {
      const result = validatePassword('StrongPass123!')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject weak passwords', () => {
      const result = validatePassword('weak')
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should enforce minimum length', () => {
      const result = validatePassword('Abc1!', { 
        minLength: 8, 
        requireUppercase: true, 
        requireLowercase: true, 
        requireNumbers: true, 
        requireSymbols: true, 
        forbidCommon: false 
      })
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password must be at least 8 characters long')
    })

    it('should enforce uppercase requirement', () => {
      const result = validatePassword('lowercase123!', {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSymbols: true,
        forbidCommon: false,
      })
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password must contain at least one uppercase letter')
    })

    it('should reject common passwords', () => {
      const result = validatePassword('password')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Please choose a less common password')
    })

    it('should handle empty password', () => {
      const result = validatePassword('')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password is required')
    })
  })

  describe('validatePasswordsMatch', () => {
    it('should validate matching passwords', () => {
      expect(validatePasswordsMatch('password123', 'password123')).toBeNull()
    })

    it('should reject non-matching passwords', () => {
      expect(validatePasswordsMatch('password123', 'different')).toBe('Passwords do not match')
    })
  })

  describe('validateProfessionalLicense', () => {
    it('should allow empty license (optional field)', () => {
      expect(validateProfessionalLicense('')).toBeNull()
    })

    it('should validate correct license formats', () => {
      expect(validateProfessionalLicense('EE123456')).toBeNull()
      expect(validateProfessionalLicense('PE 98765')).toBeNull()
      expect(validateProfessionalLicense('ABC123XYZ')).toBeNull()
    })

    it('should reject invalid license formats', () => {
      expect(validateProfessionalLicense('12345')).toBe('Professional license must be at least 6 characters')
      expect(validateProfessionalLicense('EE@123')).toBe('Professional license should contain only letters and numbers')
    })
  })

  describe('validateYearsExperience', () => {
    it('should allow undefined years (optional field)', () => {
      expect(validateYearsExperience(undefined)).toBeNull()
      expect(validateYearsExperience(null as any)).toBeNull()
    })

    it('should validate reasonable experience ranges', () => {
      expect(validateYearsExperience(0)).toBeNull()
      expect(validateYearsExperience(5)).toBeNull()
      expect(validateYearsExperience(25)).toBeNull()
      expect(validateYearsExperience(45)).toBeNull()
    })

    it('should reject invalid experience values', () => {
      expect(validateYearsExperience(-1)).toBe('Years of experience must be between 0 and 60')
      expect(validateYearsExperience(65)).toBe('Years of experience must be between 0 and 60')
    })
  })

  describe('validateLoginForm', () => {
    it('should validate correct login data', () => {
      const loginData: LoginFormData = {
        username: '<EMAIL>',
        password: 'password123',
      }
      const errors = validateLoginForm(loginData)
      expect(Object.keys(errors)).toHaveLength(0)
    })

    it('should reject missing username', () => {
      const loginData: LoginFormData = {
        username: '',
        password: 'password123',
      }
      const errors = validateLoginForm(loginData)
      expect(errors.username).toBe('Email or username is required')
    })

    it('should accept username format that is not email', () => {
      const loginData: LoginFormData = {
        username: 'valid-username',
        password: 'password123',
      }
      const errors = validateLoginForm(loginData)
      expect(errors.username).toBeUndefined()
    })

    it('should reject invalid email when email format is used', () => {
      const loginData: LoginFormData = {
        username: 'invalid@',
        password: 'password123',
      }
      const errors = validateLoginForm(loginData)
      expect(errors.username).toBe('Please enter a valid email address')
    })

    it('should reject missing password', () => {
      const loginData: LoginFormData = {
        username: '<EMAIL>',
        password: '',
      }
      const errors = validateLoginForm(loginData)
      expect(errors.password).toBe('Password is required')
    })
  })

  describe('validateRegistrationForm', () => {
    const validRegistrationData: RegisterFormData = {
      name: 'John Doe',
      email: '<EMAIL>',
      password: 'StrongPass123!',
      confirm_password: 'StrongPass123!',
      terms_accepted: true,
    }

    it('should validate correct registration data', () => {
      const errors = validateRegistrationForm(validRegistrationData)
      expect(Object.keys(errors)).toHaveLength(0)
    })

    it('should reject missing name', () => {
      const data = { ...validRegistrationData, name: '' }
      const errors = validateRegistrationForm(data)
      expect(errors.name).toBe('Name is required')
    })

    it('should reject short name', () => {
      const data = { ...validRegistrationData, name: 'Jo' }
      const errors = validateRegistrationForm(data)
      expect(errors.name).toBe('Name must be at least 3 characters long')
    })

    it('should reject weak password', () => {
      const data = { ...validRegistrationData, password: 'weak', confirm_password: 'weak' }
      const errors = validateRegistrationForm(data)
      expect(Array.isArray(errors.password)).toBe(true)
      expect((errors.password as string[]).length).toBeGreaterThan(0)
    })

    it('should reject password mismatch', () => {
      const data = { ...validRegistrationData, confirm_password: 'DifferentPass123!' }
      const errors = validateRegistrationForm(data)
      expect(errors.confirm_password).toBe('Passwords do not match')
    })

    it('should reject unaccepted terms', () => {
      const data = { ...validRegistrationData, terms_accepted: false }
      const errors = validateRegistrationForm(data)
      expect(errors.terms_accepted).toBe('You must accept the terms and conditions')
    })
  })

  describe('validatePasswordChangeForm', () => {
    const validPasswordChangeData: PasswordChangeRequest = {
      current_password: 'OldPass123!',
      new_password: 'NewStrongPass123!',
      confirm_password: 'NewStrongPass123!',
    }

    it('should validate correct password change data', () => {
      const errors = validatePasswordChangeForm(validPasswordChangeData)
      expect(Object.keys(errors)).toHaveLength(0)
    })

    it('should reject missing current password', () => {
      const data = { ...validPasswordChangeData, current_password: '' }
      const errors = validatePasswordChangeForm(data)
      expect(errors.current_password).toBe('Current password is required')
    })

    it('should reject same new password as current', () => {
      const data = {
        current_password: 'SamePass123!',
        new_password: 'SamePass123!',
        confirm_password: 'SamePass123!',
      }
      const errors = validatePasswordChangeForm(data)
      expect(errors.new_password).toBe('New password must be different from current password')
    })
  })

  describe('hasValidationErrors', () => {
    it('should return false for empty errors object', () => {
      expect(hasValidationErrors({})).toBe(false)
    })

    it('should return true when errors exist', () => {
      expect(hasValidationErrors({ email: 'Invalid email' })).toBe(true)
      expect(hasValidationErrors({ password: ['Too short', 'No symbols'] })).toBe(true)
    })

    it('should return false for null/empty values', () => {
      expect(hasValidationErrors({ email: null, password: '' })).toBe(false)
      expect(hasValidationErrors({ errors: [] })).toBe(false)
    })
  })

  describe('getFirstErrorMessage', () => {
    it('should return first error message', () => {
      const errors = {
        email: 'Invalid email',
        password: ['Too short', 'No symbols'],
      }
      expect(getFirstErrorMessage(errors)).toBe('Invalid email')
    })

    it('should return first error from array', () => {
      const errors = {
        password: ['Too short', 'No symbols'],
      }
      expect(getFirstErrorMessage(errors)).toBe('Too short')
    })

    it('should return null for empty errors', () => {
      expect(getFirstErrorMessage({})).toBeNull()
      expect(getFirstErrorMessage({ email: '', password: [] })).toBeNull()
    })
  })

  describe('getPasswordStrength', () => {
    it('should return strength for empty password', () => {
      const result = getPasswordStrength('')
      expect(result.score).toBe(0)
      expect(result.label).toBe('No password')
      expect(result.suggestions).toContain('Enter a password')
    })

    it('should return strength for weak password', () => {
      const result = getPasswordStrength('weak')
      expect(result.score).toBeLessThan(4)
      expect(['Very Weak', 'Weak', 'Fair']).toContain(result.label)
      expect(result.suggestions.length).toBeGreaterThan(0)
    })

    it('should return strength for strong password', () => {
      const result = getPasswordStrength('StrongPass123!')
      expect(result.score).toBeGreaterThanOrEqual(5)
      expect(['Strong', 'Very Strong']).toContain(result.label)
      expect(result.suggestions.length).toBeLessThanOrEqual(3)
    })

    it('should detect common passwords', () => {
      const result = getPasswordStrength('password')
      // The password 'password' is common but may get suggestions for other missing requirements first
      expect(result.suggestions.length).toBeGreaterThan(0)
    })

    it('should limit suggestions to 3', () => {
      const result = getPasswordStrength('a')
      expect(result.suggestions.length).toBeLessThanOrEqual(3)
    })
  })

  describe('Constants', () => {
    it('should export common passwords list', () => {
      expect(Array.isArray(COMMON_PASSWORDS)).toBe(true)
      expect(COMMON_PASSWORDS).toContain('password')
      expect(COMMON_PASSWORDS).toContain('123456')
    })

    it('should export email regex', () => {
      expect(EMAIL_REGEX).toBeInstanceOf(RegExp)
      expect(EMAIL_REGEX.test('<EMAIL>')).toBe(true)
      expect(EMAIL_REGEX.test('invalid')).toBe(false)
    })
  })
})