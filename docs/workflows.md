# Workflows 🔄

This document defines the standard operating procedures and methodologies for various common tasks and processes within
the Ultimate Electrical Designer project. Adhering to these workflows ensures consistency, maintains engineering-grade
quality, and facilitates efficient collaboration across all development phases.

## Client Code Quality Fixes 🛠️

**All client-side testing workflows and quality fix procedures are documented in:**

📋 **[TESTING.md - Testing Workflows](TESTING.md#6-testing-workflows)** - Complete Testing Workflow Guide

This comprehensive section includes:

- **Client-Side Code Quality Fix Workflows** (5-phase approach: Discovery & Analysis, Task Planning, Implementation,
  Verification, Documentation & Handover)
- **Feature Development Testing** workflows
- **CI/CD Testing Integration** procedures
- **Release Testing Procedures** and quality gates

**Key Workflow Features:**

- **30-minute work batches** for manageable task execution
- **Zero tolerance policies** enforcement
- **Comprehensive quality checks** (TypeScript, ESLint, <PERSON>ttier, Vitest, Playwright)
- **Test coverage validation** (85%+ and 100% standards)
- **Preventive measures** and knowledge sharing

---

## Server Code Quality Fixes 🛠️

**All server-side testing workflows and quality fix procedures are documented in:**

📋 **[TESTING.md - Testing Workflows](TESTING.md#6-testing-workflows)** - Complete Testing Workflow Guide

This comprehensive section includes:

- **Server-Side Code Quality Fix Workflows** (5-phase approach: Discovery & Analysis, Task Planning, Implementation,
  Verification, Documentation & Handover)
- **Backend Testing Strategy** with AsyncClient architecture
- **Database Testing Patterns** and authentication testing
- **Quality gates** and verification procedures

**Key Workflow Features:**

- **30-minute work batches** for manageable task execution
- **Zero tolerance policies** enforcement (100% MyPy compliance, zero Ruff errors)
- **Comprehensive quality checks** (MyPy, Ruff, pytest with coverage)
- **Test coverage validation** (85%+ and 100% standards)
- **5-layer architecture** compliance verification

---

Here is the content for the `workflows.md` file, documenting common procedures throughout the Ultimate Electrical
Designer project development:

---

## 5-Phase Implementation Methodology 🚀

This is the foundational workflow for delivering any feature or significant code change, ensuring a structured and
high-quality approach from conception to completion. It applies to both new feature development and code quality fixes
(client and server).

**Goal**: Systematic and high-quality feature delivery or issue resolution.

- **Phase 1: Discovery & Analysis** 🔍

  - **Purpose**: Understand the "what" and "why."
  - **Activities**:
    - Gather and refine requirements (e.g., user stories, functional/non-functional needs from `requirements.md`).
    - Analyze existing system, identify impacted areas (from `structure.md`, `design.md`).
    - Define scope, acceptance criteria, and success metrics.
    - For bug fixes: Reproduce the issue, identify root cause, and assess impact.
  - **Output**: Clear, unambiguous requirements or problem definition.

- **Phase 2: Task Planning** 🗓️

  - **Purpose**: Define the "how" and break down work.
  - **Activities**:
    - Break down the feature/fix into smaller, manageable tasks (referencing `General Task Template` in `tasks.md`).
    - Estimate effort for each task, aiming for **30-minute work batches**.
    - Assign tasks to team members.
    - Create a detailed plan for implementation, including necessary architectural changes or refactoring.
  - **Output**: Detailed task list, assigned responsibilities, and preliminary timeline.

- **Phase 3: Implementation** ✍️

  - **Purpose**: Write the code, build the solution.
  - **Activities**:
    - Develop code adhering to **Development Standards Enforcement** from `rules.md` (SOLID, DRY, KISS, TDD).
    - Write unit and integration tests concurrently with code (Test-Driven Development).
    - Implement features or fixes as planned.
    - Use incremental commits with clear, descriptive messages.
  - **Output**: Functional code, passing unit and integration tests.

- **Phase 4: Verification** ✅

  - **Purpose**: Ensure the solution meets all quality standards and requirements.
  - **Activities**:
    - Execute all relevant automated checks:
      - **Typing**
      - **Linting**
      - **Formatting**
      - **Testing**
    - Verify **Test Coverage Requirements** (95%+ pass rate, 100% for critical logic, 85%+ for other modules) from
      `rules.md`.
    - Perform manual testing, exploratory testing, and user acceptance testing (UAT) as needed.
    - Address any identified errors or regressions, re-entering Phase 3 if necessary.
  - **Output**: Verified, production-ready code, with all quality checks passing.

- **Phase 5: Documentation & Handover** 📖
  - **Purpose**: Document the solution for maintainability and knowledge transfer.
  - **Activities**:
    - Update API documentation (e.g., OpenAPI for backend).
    - Add/update inline code comments and docstrings (Google style for Python public methods).
    - Update `design.md` or other relevant architectural documentation if significant changes occurred.
    - Create user-facing documentation or release notes if applicable.
    - Handover knowledge to relevant teams (e.g., operations, support).
  - **Output**: Comprehensive, up-to-date documentation; team knowledge transfer.

---

## Feature Development Workflow (Agile/Scrum) 🛣️

This workflow integrates the 5-Phase Methodology into our Agile/Scrum framework for new feature delivery.

**Goal**: Deliver new features efficiently and predictably within sprints.

1. **Backlog Refinement**:

   - **Attendees**: Product Owner, Lead Engineer, Team.
   - **Activity**: Review, discuss, and refine user stories in the product backlog. Clarify requirements, acceptance
     criteria, and dependencies.
   - **Input**: `product.md`, `requirements.md`, stakeholder feedback.
   - **Output**: Ready user stories for sprint planning.

2. **Sprint Planning**:

   - **Attendees**: Team.
   - **Activity**: Select ready user stories from the backlog to commit to the sprint. Break down selected stories into
     granular tasks, leveraging **Task Planning (Phase 2)** of the 5-Phase Methodology.
   - **Output**: Sprint Backlog, defined sprint goal.

3. **Daily Scrum**:

   - **Attendees**: Team.
   - **Activity**: Short daily meeting to synchronize efforts, discuss progress, planned work, and any impediments.
   - **Output**: Aligned team, identified blockers.

4. **Development & Implementation**:

   - **Attendees**: Developers.
   - **Activity**: Work on assigned tasks following **Implementation (Phase 3)** of the 5-Phase Methodology. Prioritize
     core functionality (authentication, business logic) over secondary features.
   - **Output**: Completed tasks, new code ready for review.

5. **Code Review**:

   - **Attendees**: Developers, Reviewers.
   - **Activity**: Open a Pull Request (PR) for completed work. Automated **Pre-commit Hooks** and **CI/CD Enforcement**
     (`rules.md`) run first. Peers review code for quality, adherence to standards, design, and logic.
   - **Output**: Approved code changes, or feedback for iteration.

6. **Testing & Verification**:

   - **Attendees**: QA, Developers.
   - **Activity**: Comprehensive testing of the feature, applying the **Verification (Phase 4)** of the 5-Phase
     Methodology. This includes running all automated tests, and potentially manual/exploratory testing.
   - **Output**: Confirmed functionality, identified bugs for resolution.

7. **Sprint Review**:

   - **Attendees**: Team, Stakeholders.
   - **Activity**: Demonstrate completed work from the sprint to stakeholders. Gather feedback for future iterations.
   - **Output**: Feedback, updated product backlog.

8. **Sprint Retrospective**:
   - **Attendees**: Team.
   - **Activity**: Reflect on the sprint process. Identify what went well, what could be improved, and actionable steps
     for future sprints.
   - **Output**: Process improvements for next sprint.

---

## Continuous Integration / Continuous Delivery (CI/CD) Workflow ⚙️

This largely automated workflow ensures code quality, stability, and rapid delivery by enforcing standards at every step
from commit to deployment. It directly supports the **"Zero Tolerance Policies"** from `rules.md`.

**Goal**: Automated quality assurance and efficient, reliable deployments.

1. **Code Commit (Local)**:

   - **Actor**: Developer.
   - **Activity**: Commit code changes to the local Git repository.
   - **Pre-commit Hooks**: Automated execution of local checks (`.pre-commit-config.yaml`) including:
     - Linting and formatting validation.
     - Type checking.
     - Unit test execution for changed files for affected components.
     - Security scanning for sensitive data.
   - **Output**: Clean local commit, or errors requiring developer to fix before commit.

2. **Push to Remote & Pull Request (PR) Creation**:

   - **Actor**: Developer.
   - **Activity**: Push changes to the feature branch on the remote repository. Create a Pull Request (PR) against the
     `main` branch.
   - **Output**: PR opened, triggering CI build.

3. **CI Build Trigger & Automated Checks**:

   - **Actor**: CI System (e.g., GitHub Actions).
   - **Activity**:
     - **Pull Request Gates**: The CI/CD pipeline automatically runs all comprehensive quality checks (`rules.md`) for
       both client and server:
       - Full Linting.
       - Full Type Checking.
       - All Unit and Integration Tests.
       - End-to-End (E2E) Tests.
       - Security scans (SAST/DAST).
       - Code coverage analysis (ensuring 85%+ module coverage, 100% critical logic).
     - **Build Artifacts**: If all checks pass, build deployable artifacts (e.g., Docker images, frontend static
       assets).
   - **Output**: CI status (pass/fail) reported on the PR.

4. **Code Review & Approval**:

   - **Actor**: Reviewers, Developers.
   - **Activity**: Peers review the PR. `Review Requirements` from `rules.md` are enforced (e.g., Senior architect
     approval for architecture changes). Feedback loop until approved.
   - **Output**: Approved PR.

5. **Merge to Main Branch**:

   - **Actor**: Developer (or CI/CD system upon approval).
   - **Activity**: Merged the approved PR into the `main` branch.
   - **Output**: `main` branch updated.

6. **Continuous Delivery (CD)**:

   - **Actor**: CI/CD System.
   - **Activity**:
     - Automatic deployment of newly built artifacts to staging/pre-production environments.
     - **Deployment Gates**: Production deployment is blocked on any test failures in staging.
   - **Output**: Updated staging environment.

7. **Manual Gates/Approval (for Production)**:

   - **Actor**: Senior Engineer/Architect.
   - **Activity**: Manual review and approval before deployment to production, as per `rules.md`.
   - **Output**: Go-ahead for production deployment.

8. **Continuous Deployment (Optional/Advanced)**:
   - **Actor**: CI/CD System.
   - **Activity**: If manual gates are passed (or for fully automated pipelines), deploy to production.
   - **Output**: Updated production environment.

---

## Release Management Workflow 📦

This workflow governs how stable versions of the Ultimate Electrical Designer are prepared, deployed, and released to
end-users.

**Goal**: Deliver new software versions predictably and reliably.

1. **Release Planning**:

   - **Attendees**: Product Owner, Project Lead, Tech Leads.
   - **Activity**: Define the scope of the upcoming release, including key features, bug fixes, and target release date.
   - **Input**: `product.md`, `requirements.md`, completed features.
   - **Output**: Release scope, timeline.

2. **Branching Strategy**:

   - **Actor**: Release Manager/Lead Engineer.
   - **Activity**: Create a dedicated release branch (e.g., `release/vX.Y.Z`) from the `main` branch. All subsequent bug
     fixes for the release will be made on this branch and potentially cherry-picked to `main`.
   - **Output**: Release branch created.

3. **Stabilization Period**:

   - **Actor**: Development Team.
   - **Activity**: Focus exclusively on critical bug fixes and performance optimizations on the release branch. No new
     features are merged into this branch.
   - **Output**: Stabilized release candidate.

4. **User Acceptance Testing (UAT)**:

   - **Actor**: Product Owner, Key Stakeholders, End-Users.
   - **Activity**: Conduct testing in a production-like environment to ensure the release candidate meets business
     requirements and user expectations.
   - **Output**: UAT sign-off, or identified bugs for immediate resolution.

5. **Documentation Finalization**:

   - **Actor**: Documentation Lead, Developers.
   - **Activity**: Update all relevant documentation, including user manuals, release notes, API specifications, and
     internal technical docs (`design.md`, `requirements.md`).
   - **Output**: Complete and up-to-date documentation.

6. **Deployment**:

   - **Actor**: Operations/DevOps Team, following **CI/CD Workflow**.
   - **Activity**: Deploy the release candidate to the production environment.
   - **Output**: New version live in production.

7. **Post-Deployment Monitoring**:

   - **Actor**: Operations/Monitoring Team.
   - **Activity**: Closely monitor system health, performance metrics, and error logs immediately after deployment.
   - **Input**: **Monitoring Alerts** (`rules.md`).
   - **Output**: Early detection of issues, performance insights.

8. **Rollback Plan**:

   - **Actor**: Operations/DevOps Team.
   - **Activity**: Have a well-defined and tested rollback procedure ready in case of critical issues post-deployment.
   - **Output**: Readiness for rapid recovery.

9. **Announcement & Communication**:
   - **Actor**: Product Owner, Marketing.
   - **Activity**: Communicate the new release to internal teams, and external users if applicable.
   - **Output**: Stakeholder and user awareness.

---

## Incident Management Workflow 🚨

This workflow outlines the structured process for responding to, resolving, and learning from production incidents or
service disruptions.

**Goal**: Minimize impact of incidents and prevent recurrence.

1. **Detection**:

   - **Actor**: Monitoring Systems, Users, Support.
   - **Activity**: Identify that an incident has occurred.
   - **Input**: **Monitoring Alerts** (`rules.md`), user reports.
   - **Output**: Initial incident notification.

2. **Triaging & Prioritization**:

   - **Actor**: On-call Engineer/Incident Commander.
   - **Activity**: Assess the impact (severity) and urgency of the incident. Assign a priority level.
   - **Output**: Incident categorized and prioritized.

3. **Notification & Communication**:

   - **Actor**: Incident Commander.
   - **Activity**: Alert relevant engineering teams, stakeholders, and potentially affected users. Establish
     communication channels.
   - **Output**: Informed teams and stakeholders.

4. **Investigation & Diagnosis**:

   - **Actor**: Responding Team(s).
   - **Activity**: Identify the root cause of the incident using logs, monitoring tools, and system knowledge.
   - **Output**: Identified root cause (or likely root cause).

5. **Resolution**:

   - **Actor**: Responding Team(s).
   - **Activity**: Implement a fix or workaround to restore service functionality. This might involve hotfixes, code
     reverts, or configuration changes.
   - **Output**: Service restored.

6. **Verification**:

   - **Actor**: Responding Team(s), QA.
   - **Activity**: Confirm that the fix has resolved the incident and the service is stable. This may involve re-running
     tests or direct observation.
   - **Output**: Confirmed resolution.

7. **Post-Incident Review (PIR)**:

   - **Actor**: Incident Commander, Responding Teams.
   - **Activity**: Conduct a **blameless** post-mortem analysis of the incident. Document what happened, why, what was
     done, and what can be done to prevent recurrence.
   - **Output**: PIR report, actionable preventative items (e.g., new tests, monitoring, documentation updates).

8. **Documentation**:
   - **Actor**: Incident Commander, Team.
   - **Activity**: Record the incident details, its resolution, and PIR findings in an incident knowledge base.
   - **Output**: Updated incident knowledge base.

---

## Code Review Workflow 🤝

This workflow details the process for ensuring high-quality code through peer review, upholding the **"Zero Tolerance
Policies"** for code quality from `rules.md`.

**Goal**: Enhance code quality, share knowledge, and reduce defects.

1. **Feature Branch Creation**:

   - **Actor**: Developer.
   - **Activity**: Create a new, descriptively named feature branch from `main` (or a release branch for fixes).
   - **Output**: Dedicated feature branch.

2. **Local Development & Testing**:

   - **Actor**: Developer.
   - **Activity**: Write code, strictly following **Development Standards Enforcement** (SOLID, DRY, KISS, TDD) and
     implementing required tests. Ensure local tests pass.
   - **Pre-commit Checks**: Run local `pre-commit` hooks before committing.
   - **Output**: Code and passing local tests.

3. **Push to Remote & Pull Request (PR) Creation**:

   - **Actor**: Developer.
   - **Activity**: Push the feature branch to the remote repository. Create a Pull Request (PR) in the version control
     system (e.g., GitHub).
   - **PR Description**: Include a clear description of changes, purpose, relevant issues, and testing instructions.
   - **Output**: PR awaiting review.

4. **Automated Checks (CI)**:

   - **Actor**: CI System.
   - **Activity**: The CI/CD pipeline (`CI/CD Enforcement` from `rules.md`) automatically runs all comprehensive quality
     checks (linting, type checking, unit tests, integration tests, E2E tests, security scans).
   - **Output**: CI status (pass/fail) reported on the PR. PR cannot be merged if these fail.

5. **Peer Review**:

   - **Actor**: Reviewers (assigned by developer, or automatically).
   - **Activity**:
     - Review the code for: design, logic correctness, adherence to coding standards (`rules.md`), test coverage, error
       handling, performance implications, and security considerations.
     - Provide constructive, actionable feedback as comments on the PR.
     - **Best Practices**: Keep PRs small (<400 LOC for easier review), timebox reviews (<60 minutes), use checklists.
     - **Specific Approvals**: Ensure `Architecture Changes` require senior architect approval, and `Security Changes`
       require security team review (`rules.md`).
   - **Output**: Review comments, or initial approval.

6. **Feedback & Iteration**:

   - **Actor**: Developer.
   - **Activity**: Address review comments by updating the code. Push new commits to the same branch.
   - **Output**: Updated code, re-triggering automated checks.

7. **Approval & Merge**:
   - **Actor**: Reviewers, Developer.
   - **Activity**: Once all automated checks pass, all comments are addressed, and reviewers approve the changes, the PR
     is merged into the `main` branch.
   - **Output**: Code integrated into the main codebase.
